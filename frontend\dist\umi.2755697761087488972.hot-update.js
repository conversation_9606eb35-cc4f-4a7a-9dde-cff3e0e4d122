globalThis.makoModuleHotUpdate('src/.umi/umi.ts?hmr', {
    modules: {
        "src/app.tsx": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            function _export(target, all) {
                for(var name in all)Object.defineProperty(target, name, {
                    enumerable: true,
                    get: all[name]
                });
            }
            __mako_require__.e(exports, {
                getInitialState: function() {
                    return getInitialState;
                },
                layout: function() {
                    return layout;
                },
                request: function() {
                    return request;
                }
            });
            var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
            var _procomponents = __mako_require__("node_modules/@ant-design/pro-components/es/index.js");
            var _max = __mako_require__("src/.umi/exports.ts");
            var _components = __mako_require__("src/components/index.ts");
            var _FloatButton = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/components/FloatButton/index.tsx"));
            var _services = __mako_require__("src/services/index.ts");
            var _tokenUtils = __mako_require__("src/utils/tokenUtils.ts");
            var _defaultSettings = /*#__PURE__*/ _interop_require_default._(__mako_require__("config/defaultSettings.ts"));
            var _requestErrorConfig = __mako_require__("src/requestErrorConfig.ts");
            __mako_require__("node_modules/@ant-design/v5-patch-for-react-19/es/index.js");
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            const isDev = true;
            const loginPath = '/user/login';
            const teamSelectPath = '/user/team-select';
            const teamCreatePath = '/team/create';
            // 不需要认证的路径
            const noAuthPaths = [
                loginPath,
                teamSelectPath,
                '/404'
            ];
            /**
 * 检查Token中是否包含团队信息
 */ const hasTeamInToken = ()=>{
                const token = localStorage.getItem('auth_token');
                if (!token) return false;
                const payload = (0, _tokenUtils.parseJwtPayload)(token);
                return payload && payload.teamId != null;
            };
            async function getInitialState() {
                const fetchUserInfo = async ()=>{
                    try {
                        if (!_services.AuthService.isLoggedIn()) return undefined;
                        const userProfile = await _services.UserService.getUserProfile();
                        return userProfile;
                    } catch (error) {
                        var _error_message, _error_message1;
                        console.error('获取用户信息失败:', error);
                        // 只有在确实是认证错误时才清除 Token
                        if ((error === null || error === void 0 ? void 0 : (_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes('401')) || (error === null || error === void 0 ? void 0 : (_error_message1 = error.message) === null || _error_message1 === void 0 ? void 0 : _error_message1.includes('未认证'))) _services.AuthService.clearToken();
                        return undefined;
                    }
                };
                const fetchTeamInfo = async ()=>{
                    try {
                        const teamDetail = await _services.TeamService.getCurrentTeamDetail();
                        return teamDetail;
                    } catch (error) {
                        console.error('获取团队信息失败:', error);
                        return undefined;
                    }
                };
                const { location } = _max.history;
                const currentPath = location.pathname;
                // 如果是不需要认证的页面，直接返回基础状态
                if (noAuthPaths.some((path)=>currentPath.startsWith(path))) return {
                    fetchUserInfo,
                    fetchTeamInfo,
                    settings: _defaultSettings.default
                };
                // 检查认证状态 - 直接检查 localStorage 避免时序问题
                const token = localStorage.getItem('auth_token');
                const hasToken = !!token;
                // 使用 localStorage 直接检查，避免 AuthService 的时序问题
                if (!hasToken) {
                    _max.history.push(loginPath);
                    return {
                        fetchUserInfo,
                        fetchTeamInfo,
                        settings: _defaultSettings.default
                    };
                }
                // 获取用户信息
                const currentUser = await fetchUserInfo();
                if (!currentUser) {
                    _max.history.push(loginPath);
                    return {
                        fetchUserInfo,
                        fetchTeamInfo,
                        settings: _defaultSettings.default
                    };
                }
                // 获取团队信息
                const currentTeam = await fetchTeamInfo();
                // 如果没有团队信息且不在团队相关页面或个人中心页面，跳转到团队选择页面
                if (!currentTeam && ![
                    teamSelectPath,
                    teamCreatePath,
                    '/personal-center'
                ].includes(currentPath)) {
                    _max.history.push(teamSelectPath);
                    return {
                        fetchUserInfo,
                        fetchTeamInfo,
                        currentUser,
                        settings: _defaultSettings.default
                    };
                }
                return {
                    fetchUserInfo,
                    fetchTeamInfo,
                    currentUser,
                    currentTeam,
                    settings: _defaultSettings.default
                };
            }
            const layout = ({ initialState, setInitialState })=>{
                var _initialState_currentUser, _initialState_currentTeam;
                return {
                    actionsRender: ()=>[
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_components.Question, {}, "doc", false, {
                                fileName: "src/app.tsx",
                                lineNumber: 155,
                                columnNumber: 7
                            }, this)
                        ],
                    // 移除头像下拉菜单，使用浮动按钮替代
                    avatarProps: false,
                    waterMarkProps: {
                        content: initialState === null || initialState === void 0 ? void 0 : (_initialState_currentUser = initialState.currentUser) === null || _initialState_currentUser === void 0 ? void 0 : _initialState_currentUser.name
                    },
                    footerRender: ()=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_components.Footer, {}, void 0, false, {
                            fileName: "src/app.tsx",
                            lineNumber: 162,
                            columnNumber: 25
                        }, this),
                    onPageChange: ()=>{
                        const { location } = _max.history;
                        const currentPath = location.pathname;
                        // 如果是不需要认证的页面，不做处理
                        if (noAuthPaths.some((path)=>currentPath.startsWith(path))) return;
                        // 检查登录状态
                        if (!_services.AuthService.isLoggedIn()) {
                            _max.history.push(loginPath);
                            return;
                        }
                        // 检查团队选择状态 - 优先检查Token中的团队信息，避免时序问题
                        const hasTeamToken = hasTeamInToken();
                        const hasTeamInState = !!(initialState === null || initialState === void 0 ? void 0 : initialState.currentTeam);
                        // 如果Token中有团队信息或者initialState中有团队信息，则认为已选择团队
                        const hasSelectedTeam = hasTeamToken || hasTeamInState;
                        // 只有在确实没有选择团队时才跳转到团队选择页面
                        if (!hasSelectedTeam && ![
                            teamSelectPath,
                            teamCreatePath,
                            '/personal-center'
                        ].includes(currentPath)) {
                            console.log('未选择团队，跳转到团队选择页面', {
                                hasTeamToken,
                                hasTeamInState,
                                currentPath
                            });
                            _max.history.push(teamSelectPath);
                            return;
                        }
                    },
                    bgLayoutImgList: [],
                    links: [],
                    menuHeaderRender: undefined,
                    childrenRender: (children)=>{
                        return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_components.ErrorBoundary, {
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_components.GlobalLoading, {
                                children: [
                                    children,
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_FloatButton.default, {}, void 0, false, {
                                        fileName: "src/app.tsx",
                                        lineNumber: 201,
                                        columnNumber: 13
                                    }, this),
                                    isDev && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.SettingDrawer, {
                                        disableUrlParams: true,
                                        enableDarkTheme: true,
                                        settings: initialState === null || initialState === void 0 ? void 0 : initialState.settings,
                                        onSettingChange: (settings)=>{
                                            setInitialState((preInitialState)=>({
                                                    ...preInitialState,
                                                    settings
                                                }));
                                        }
                                    }, void 0, false, {
                                        fileName: "src/app.tsx",
                                        lineNumber: 203,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/app.tsx",
                                lineNumber: 198,
                                columnNumber: 11
                            }, this)
                        }, void 0, false, {
                            fileName: "src/app.tsx",
                            lineNumber: 197,
                            columnNumber: 9
                        }, this);
                    },
                    title: (initialState === null || initialState === void 0 ? void 0 : (_initialState_currentTeam = initialState.currentTeam) === null || _initialState_currentTeam === void 0 ? void 0 : _initialState_currentTeam.name) || 'TeamAuth',
                    logo: '/logo.svg',
                    ...initialState === null || initialState === void 0 ? void 0 : initialState.settings
                };
            };
            const request = {
                baseURL: '/api',
                ..._requestErrorConfig.errorConfig
            };
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        }
    }
}, function(runtime) {
    runtime._h = '5489502646934583622';
    runtime.updateEnsure2Map({
        "src/.umi/core/EmptyRoute.tsx": [
            "src/.umi/core/EmptyRoute.tsx"
        ],
        "src/.umi/plugin-layout/Layout.tsx": [
            "vendors",
            "src/.umi/plugin-layout/Layout.tsx"
        ],
        "src/.umi/plugin-openapi/openapi.tsx": [
            "vendors",
            "src/.umi/plugin-openapi/openapi.tsx"
        ],
        "src/pages/404.tsx": [
            "p__404"
        ],
        "src/pages/Dashboard/index.tsx": [
            "p__Dashboard__index"
        ],
        "src/pages/friend/index.tsx": [
            "p__friend__index"
        ],
        "src/pages/help/index.tsx": [
            "p__help__index"
        ],
        "src/pages/personal-center/index.tsx": [
            "src/pages/personal-center/index.tsx"
        ],
        "src/pages/subscription/index.tsx": [
            "p__subscription__index"
        ],
        "src/pages/team/detail/index.tsx": [
            "p__team__detail__index"
        ],
        "src/pages/team/index.tsx": [
            "p__team__index"
        ],
        "src/pages/user/index.tsx": [
            "p__user__index"
        ],
        "src/pages/user/login/index.tsx": [
            "p__user__login__index"
        ]
    });
    ;
});

//# sourceMappingURL=umi.2755697761087488972.hot-update.js.map