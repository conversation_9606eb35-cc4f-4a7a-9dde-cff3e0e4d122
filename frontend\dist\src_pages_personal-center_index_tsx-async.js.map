{"version": 3, "sources": ["src/pages/personal-center/TeamListCard.tsx", "src/pages/personal-center/TodoManagement.tsx", "src/pages/personal-center/UserProfileCard.tsx", "src/pages/personal-center/index.tsx", "src/services/todo.ts"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport {\r\n  Card,\r\n  Typography,\r\n  Tooltip,\r\n  List,\r\n  Flex,\r\n  Spin,\r\n  Alert,\r\n  message,\r\n  Row,\r\n  Col\r\n} from \"antd\";\r\nimport { TeamService } from \"@/services/team\";\r\nimport { AuthService } from \"@/services\";\r\nimport { useModel, history } from '@umijs/max';\r\nimport type { TeamDetailResponse } from \"@/types/api\";\r\nimport {\r\n  CarOutlined,\r\n  TeamOutlined,\r\n  UserOutlined,\r\n  ClockCircleOutlined,\r\n  CrownOutlined,\r\n  RightOutlined,\r\n  ExclamationCircleOutlined\r\n} from \"@ant-design/icons\";\r\n\r\nconst { Text, Title } = Typography;\r\n\r\n// 响应式布局样式\r\nconst styles = `\r\n  .team-item .ant-card-body {\r\n    padding: 0 !important;\r\n  }\r\n\r\n  .team-item:hover {\r\n    transform: translateY(-1px);\r\n    box-shadow: 0 4px 12px rgba(0,0,0,0.1) !important;\r\n  }\r\n\r\n  @media (max-width: 768px) {\r\n    .team-item {\r\n      margin-bottom: 8px;\r\n    }\r\n\r\n    .team-stats-row {\r\n      margin-top: 8px;\r\n    }\r\n  }\r\n\r\n  @media (max-width: 576px) {\r\n    .team-stats-row {\r\n      margin-top: 12px;\r\n    }\r\n\r\n    .team-stats-col {\r\n      margin-bottom: 4px;\r\n    }\r\n\r\n    .team-info-wrap {\r\n      gap: 8px !important;\r\n    }\r\n  }\r\n\r\n  @media (max-width: 480px) {\r\n    .team-name-text {\r\n      font-size: 14px !important;\r\n    }\r\n\r\n    .team-meta-text {\r\n      font-size: 11px !important;\r\n    }\r\n  }\r\n`;\r\n\r\nconst TeamListCard: React.FC = () => {\r\n  // 团队列表状态管理\r\n  const [teams, setTeams] = useState<TeamDetailResponse[]>([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [switchingTeamId, setSwitchingTeamId] = useState<number | null>(null);\r\n\r\n  const { initialState, setInitialState } = useModel('@@initialState');\r\n  const currentTeam = initialState?.currentTeam;\r\n\r\n  // 获取团队列表数据\r\n  useEffect(() => {\r\n    const fetchTeams = async () => {\r\n      try {\r\n        setLoading(true);\r\n        setError(null);\r\n        const teamsData = await TeamService.getUserTeamsWithStats();\r\n        setTeams(teamsData);\r\n      } catch (error) {\r\n        console.error('获取团队列表失败:', error);\r\n        setError('获取团队列表失败');\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchTeams();\r\n  }, []);\r\n\r\n  // 团队切换处理函数\r\n  const handleTeamSwitch = async (teamId: number, teamName: string) => {\r\n    try {\r\n      setSwitchingTeamId(teamId);\r\n\r\n      // 如果是当前团队，直接跳转到仪表盘，不需要调用切换API\r\n      if (teamId === currentTeam?.id) {\r\n        message.success(`进入团队：${teamName}`);\r\n        history.push('/dashboard');\r\n        return;\r\n      }\r\n\r\n      // 非当前团队，执行切换逻辑\r\n      const response = await AuthService.selectTeam({ teamId });\r\n\r\n      // 检查后端返回的团队选择成功标识\r\n      if (response.teamSelectionSuccess && response.team && response.team.id === teamId) {\r\n        message.success(`已切换到团队：${teamName}`);\r\n\r\n        // 同步更新 initialState，等待更新完成后再跳转\r\n        if (initialState?.fetchTeamInfo && initialState?.fetchUserInfo && setInitialState) {\r\n          try {\r\n            const [currentUser, currentTeam] = await Promise.all([\r\n              initialState.fetchUserInfo(),\r\n              initialState.fetchTeamInfo()\r\n            ]);\r\n\r\n            // 确保团队信息已正确获取\r\n            if (currentTeam && currentTeam.id === teamId) {\r\n              await setInitialState({\r\n                ...initialState,\r\n                currentUser,\r\n                currentTeam,\r\n              });\r\n\r\n              // 等待 initialState 更新完成后再跳转到仪表盘\r\n              setTimeout(() => {\r\n                history.push('/dashboard');\r\n              }, 100);\r\n            } else {\r\n              console.error('获取的团队信息与选择的团队不匹配');\r\n              message.error('团队切换失败，请重试');\r\n            }\r\n          } catch (error) {\r\n            console.error('更新 initialState 失败:', error);\r\n            message.error('团队切换失败，请重试');\r\n          }\r\n        } else {\r\n          // 如果没有 initialState 相关方法，直接跳转\r\n          history.push('/dashboard');\r\n        }\r\n      } else {\r\n        console.error('团队切换响应异常，未返回正确的团队信息');\r\n        message.error('团队切换失败，请重试');\r\n      }\r\n    } catch (error) {\r\n      console.error('团队切换失败:', error);\r\n      message.error('团队切换失败');\r\n    } finally {\r\n      setSwitchingTeamId(null);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <>\r\n      {/* 注入样式 */}\r\n      <style dangerouslySetInnerHTML={{ __html: styles }} />\r\n\r\n      <Card\r\n        className=\"dashboard-card\"\r\n        style={{\r\n          borderRadius: 16,\r\n          boxShadow: \"0 6px 20px rgba(0,0,0,0.08)\",\r\n          border: \"none\",\r\n          background: \"linear-gradient(145deg, #ffffff, #f8faff)\",\r\n        }}\r\n        title={\r\n          <Flex justify=\"space-between\" align=\"center\">\r\n            <Title level={4} style={{\r\n              margin: 0,\r\n              background: 'linear-gradient(135deg, #1890ff, #722ed1)',\r\n              WebkitBackgroundClip: 'text',\r\n              WebkitTextFillColor: 'transparent',\r\n              fontWeight: 600\r\n            }}>\r\n              团队列表\r\n            </Title>\r\n          </Flex>\r\n        }\r\n      >\r\n      {error ? (\r\n        <Alert\r\n          message=\"团队列表加载失败\"\r\n          description={error}\r\n          type=\"error\"\r\n          showIcon\r\n          style={{ marginBottom: 16 }}\r\n        />\r\n      ) : (\r\n        <Spin spinning={loading}>\r\n          <List\r\n            dataSource={teams}\r\n            renderItem={(item) => (\r\n              <List.Item>\r\n                <Card\r\n                  className=\"team-item\"\r\n                  style={{\r\n                    background: currentTeam?.id === item.id\r\n                      ? \"linear-gradient(135deg, #f0f9ff, #e6f4ff)\"\r\n                      : \"#fff\",\r\n                    borderRadius: 8,\r\n                    boxShadow: currentTeam?.id === item.id\r\n                      ? \"0 2px 8px rgba(24, 144, 255, 0.12)\"\r\n                      : \"0 1px 4px rgba(0,0,0,0.06)\",\r\n                    width: \"100%\",\r\n                    borderLeft: `3px solid ${item.isCreator ? \"#722ed1\" : \"#52c41a\"}`,\r\n                    transition: \"all 0.3s cubic-bezier(0.4, 0, 0.2, 1)\",\r\n                    border: currentTeam?.id === item.id\r\n                      ? \"1px solid #91caff\"\r\n                      : \"1px solid #f0f0f0\",\r\n                    padding: \"12px 16px\",\r\n                    position: 'relative',\r\n                    overflow: 'hidden'\r\n                  }}\r\n                  hoverable\r\n                  onMouseEnter={(e) => {\r\n                    if (currentTeam?.id !== item.id) {\r\n                      e.currentTarget.style.transform = 'translateY(-2px)';\r\n                      e.currentTarget.style.boxShadow = '0 8px 24px rgba(0,0,0,0.12)';\r\n                    }\r\n                  }}\r\n                  onMouseLeave={(e) => {\r\n                    if (currentTeam?.id !== item.id) {\r\n                      e.currentTarget.style.transform = 'translateY(0)';\r\n                      e.currentTarget.style.boxShadow = '0 2px 8px rgba(0,0,0,0.06)';\r\n                    }\r\n                  }}\r\n                >\r\n\r\n                  {/* 响应式布局 */}\r\n                  <Row gutter={[8, 8]} align=\"middle\" style={{ width: '100%' }}>\r\n                    {/* 左侧：团队信息 */}\r\n                    <Col xs={24} sm={24} md={14} lg={12} xl={14}>\r\n                      <Flex vertical gap={6}>\r\n                        {/* 团队名称行 */}\r\n                        <Flex align=\"center\" gap={8} wrap=\"wrap\">\r\n                          <div\r\n                            style={{\r\n                              cursor: 'pointer',\r\n                              padding: '2px 4px',\r\n                              borderRadius: 4,\r\n                              transition: 'all 0.2s ease',\r\n                              display: 'flex',\r\n                              alignItems: 'center',\r\n                              gap: 6\r\n                            }}\r\n                            onClick={() => handleTeamSwitch(item.id, item.name)}\r\n                            onMouseEnter={(e) => {\r\n                              e.currentTarget.style.background = 'rgba(24, 144, 255, 0.05)';\r\n                            }}\r\n                            onMouseLeave={(e) => {\r\n                              e.currentTarget.style.background = 'transparent';\r\n                            }}\r\n                          >\r\n                            <Text\r\n                              strong\r\n                              style={{\r\n                                fontSize: 16,\r\n                                color: currentTeam?.id === item.id ? '#1890ff' : '#262626',\r\n                                lineHeight: 1.2\r\n                              }}\r\n                            >\r\n                              {item.name}\r\n                            </Text>\r\n                            <RightOutlined\r\n                              style={{\r\n                                fontSize: 10,\r\n                                color: currentTeam?.id === item.id ? '#1890ff' : '#8c8c8c',\r\n                                verticalAlign: 'middle',\r\n                                display: 'inline-flex',\r\n                                alignItems: 'center'\r\n                              }}\r\n                            />\r\n                          </div>\r\n\r\n                          {/* 状态标识 */}\r\n                          {currentTeam?.id === item.id && (\r\n                            <span style={{\r\n                              background: '#1890ff',\r\n                              color: 'white',\r\n                              padding: '1px 6px',\r\n                              borderRadius: 8,\r\n                              fontSize: 10,\r\n                              fontWeight: 500\r\n                            }}>\r\n                              当前\r\n                            </span>\r\n                          )}\r\n\r\n                          {switchingTeamId === item.id && (\r\n                            <Flex align=\"center\" gap={4}>\r\n                              <Spin size=\"small\" />\r\n                              <Text style={{ fontSize: 10, color: '#666' }}>切换中</Text>\r\n                            </Flex>\r\n                          )}\r\n                        </Flex>\r\n\r\n                        {/* 团队基本信息 */}\r\n                        <Flex align=\"center\" gap={12} wrap=\"wrap\">\r\n                          <Tooltip title={`创建时间: ${new Date(item.createdAt).toLocaleString('zh-CN')}`}>\r\n                            <Flex align=\"center\" gap={4}>\r\n                              <ClockCircleOutlined style={{ color: \"#8c8c8c\", fontSize: 12 }} />\r\n                              <Text style={{ fontSize: 12, color: '#8c8c8c' }}>\r\n                                {new Date(item.createdAt).toLocaleDateString('zh-CN')}\r\n                              </Text>\r\n                            </Flex>\r\n                          </Tooltip>\r\n\r\n                          <Tooltip title={`团队成员: ${item.memberCount}人`}>\r\n                            <Flex align=\"center\" gap={4}>\r\n                              <TeamOutlined style={{ color: \"#8c8c8c\", fontSize: 12 }} />\r\n                              <Text style={{ fontSize: 12, color: '#8c8c8c' }}>\r\n                                {item.memberCount} 人\r\n                              </Text>\r\n                            </Flex>\r\n                          </Tooltip>\r\n\r\n                          {/* 角色标识 */}\r\n                          <span style={{\r\n                            background: item.isCreator ? '#722ed1' : '#52c41a',\r\n                            color: 'white',\r\n                            padding: '2px 6px',\r\n                            borderRadius: 8,\r\n                            fontSize: 10,\r\n                            fontWeight: 500,\r\n                            display: 'flex',\r\n                            alignItems: 'center',\r\n                            gap: 2\r\n                          }}>\r\n                            {item.isCreator ? (\r\n                              <>\r\n                                <CrownOutlined style={{ fontSize: 9 }} />\r\n                                管理员\r\n                              </>\r\n                            ) : (\r\n                              <>\r\n                                <UserOutlined style={{ fontSize: 9 }} />\r\n                                成员\r\n                              </>\r\n                            )}\r\n                          </span>\r\n                        </Flex>\r\n                      </Flex>\r\n                    </Col>\r\n\r\n                    {/* 右侧：响应式指标卡片 */}\r\n                    <Col xs={24} sm={24} md={10} lg={12} xl={10}>\r\n                      <Row gutter={[4, 4]} justify={{ xs: \"start\", md: \"end\" }}>\r\n                        {/* 车辆资源 */}\r\n                        <Col xs={6} sm={6} md={6} lg={6} xl={6}>\r\n                          <div style={{\r\n                            background: '#f0f7ff',\r\n                            border: '1px solid #d9e8ff',\r\n                            borderRadius: 6,\r\n                            padding: '4px 6px',\r\n                            textAlign: 'center',\r\n                            minWidth: '45px'\r\n                          }}>\r\n                            <Flex vertical align=\"center\" gap={1}>\r\n                              <CarOutlined style={{ color: \"#1890ff\", fontSize: 12 }} />\r\n                              <Text strong style={{ fontSize: 14, color: '#1890ff', lineHeight: 1 }}>\r\n                                {item.stats?.vehicles || 0}\r\n                              </Text>\r\n                              <Text style={{ fontSize: 8, color: '#666' }}>车辆</Text>\r\n                            </Flex>\r\n                          </div>\r\n                        </Col>\r\n\r\n                        {/* 人员资源 */}\r\n                        <Col xs={6} sm={6} md={6} lg={6} xl={6}>\r\n                          <div style={{\r\n                            background: '#f6ffed',\r\n                            border: '1px solid #d1f0be',\r\n                            borderRadius: 6,\r\n                            padding: '4px 6px',\r\n                            textAlign: 'center',\r\n                            minWidth: '45px'\r\n                          }}>\r\n                            <Flex vertical align=\"center\" gap={1}>\r\n                              <UserOutlined style={{ color: \"#52c41a\", fontSize: 12 }} />\r\n                              <Text strong style={{ fontSize: 14, color: '#52c41a', lineHeight: 1 }}>\r\n                                {item.stats?.personnel || 0}\r\n                              </Text>\r\n                              <Text style={{ fontSize: 8, color: '#666' }}>人员</Text>\r\n                            </Flex>\r\n                          </div>\r\n                        </Col>\r\n\r\n                        {/* 临期事项 */}\r\n                        <Col xs={6} sm={6} md={6} lg={6} xl={6}>\r\n                          <div style={{\r\n                            background: '#fff7e6',\r\n                            border: '1px solid #ffd666',\r\n                            borderRadius: 6,\r\n                            padding: '4px 6px',\r\n                            textAlign: 'center',\r\n                            minWidth: '45px'\r\n                          }}>\r\n                            <Flex vertical align=\"center\" gap={1}>\r\n                              <ExclamationCircleOutlined style={{ color: \"#faad14\", fontSize: 12 }} />\r\n                              <Text strong style={{ fontSize: 14, color: '#faad14', lineHeight: 1 }}>\r\n                                {item.stats?.expiring || 0}\r\n                              </Text>\r\n                              <Text style={{ fontSize: 8, color: '#666' }}>临期</Text>\r\n                            </Flex>\r\n                          </div>\r\n                        </Col>\r\n\r\n                        {/* 逾期事项 */}\r\n                        <Col xs={6} sm={6} md={6} lg={6} xl={6}>\r\n                          <div style={{\r\n                            background: '#fff1f0',\r\n                            border: '1px solid #ffccc7',\r\n                            borderRadius: 6,\r\n                            padding: '4px 6px',\r\n                            textAlign: 'center',\r\n                            minWidth: '45px'\r\n                          }}>\r\n                            <Flex vertical align=\"center\" gap={1}>\r\n                              <ExclamationCircleOutlined style={{ color: \"#ff4d4f\", fontSize: 12 }} />\r\n                              <Text strong style={{ fontSize: 14, color: '#ff4d4f', lineHeight: 1 }}>\r\n                                {item.stats?.overdue || 0}\r\n                              </Text>\r\n                              <Text style={{ fontSize: 8, color: '#666' }}>逾期</Text>\r\n                            </Flex>\r\n                          </div>\r\n                        </Col>\r\n                      </Row>\r\n                    </Col>\r\n                  </Row>\r\n                </Card>\r\n              </List.Item>\r\n            )}\r\n          />\r\n        </Spin>\r\n      )}\r\n    </Card>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default TeamListCard;", "import React, { useState, useEffect } from \"react\";\r\nimport {\r\n  <PERSON><PERSON>,\r\n  <PERSON>,\r\n  Col,\r\n  Dropdown,\r\n  Flex,\r\n  Form,\r\n  Input,\r\n  List,\r\n  Modal,\r\n  Progress,\r\n  Row,\r\n  Select,\r\n  Space,\r\n  Tabs,\r\n  Tooltip,\r\n  Typography,\r\n  Spin,\r\n  Alert,\r\n  message,\r\n} from \"antd\";\r\nimport { TodoService } from \"@/services/todo\";\r\nimport type { TodoResponse, TodoStatsResponse } from \"@/types/api\";\r\nimport {\r\n  CheckOutlined,\r\n  DeleteOutlined,\r\n  EditOutlined,\r\n  MoreOutlined,\r\n  PlusOutlined,\r\n  SearchOutlined,\r\n  CalendarOutlined,\r\n} from \"@ant-design/icons\";\r\n\r\nconst { Text } = Typography;\r\nconst { TabPane } = Tabs;\r\n\r\n// 使用API类型定义，不需要重复定义接口\r\ninterface TodoManagementProps {\r\n  onAddTodo?: (todo: TodoResponse) => void;\r\n  onUpdateTodo?: (id: number, updatedTodo: Partial<TodoResponse>) => void;\r\n  onDeleteTodo?: (id: number) => void;\r\n}\r\n\r\nconst TodoManagement: React.FC<TodoManagementProps> = (props) => {\r\n  // TODO数据状态管理\r\n  const [personalTasks, setPersonalTasks] = useState<TodoResponse[]>([]);\r\n  const [todoStats, setTodoStats] = useState<TodoStatsResponse>({\r\n    highPriorityCount: 0,\r\n    mediumPriorityCount: 0,\r\n    lowPriorityCount: 0,\r\n    totalCount: 0,\r\n    completedCount: 0,\r\n    completionPercentage: 0,\r\n  });\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n\r\n  // 待办事项状态管理\r\n  const [todoModalVisible, setTodoModalVisible] = useState(false);\r\n  const [todoForm] = Form.useForm();\r\n  const [editingTodoId, setEditingTodoId] = useState<number | null>(null);\r\n\r\n  // 过滤器状态\r\n  const [activeTab, setActiveTab] = useState<\"all\" | \"pending\" | \"completed\">(\r\n    \"pending\"\r\n  );\r\n  const [searchText, setSearchText] = useState(\"\");\r\n\r\n  // 获取TODO数据\r\n  useEffect(() => {\r\n    const fetchTodoData = async () => {\r\n      try {\r\n        setLoading(true);\r\n        setError(null);\r\n\r\n        console.log('TodoManagement: 开始获取TODO数据');\r\n\r\n        // 分别获取TODO列表和统计数据，避免一个失败影响另一个\r\n        const todosPromise = TodoService.getUserTodos().catch(error => {\r\n          console.error('获取TODO列表失败:', error);\r\n          return [];\r\n        });\r\n\r\n        const statsPromise = TodoService.getTodoStats().catch(error => {\r\n          console.error('获取TODO统计失败:', error);\r\n          return {\r\n            highPriorityCount: 0,\r\n            mediumPriorityCount: 0,\r\n            lowPriorityCount: 0,\r\n            totalCount: 0,\r\n            completedCount: 0,\r\n            completionPercentage: 0,\r\n          };\r\n        });\r\n\r\n        const [todos, stats] = await Promise.all([todosPromise, statsPromise]);\r\n\r\n        console.log('TodoManagement: 获取到TODO列表:', todos);\r\n        console.log('TodoManagement: 获取到统计数据:', stats);\r\n\r\n        setPersonalTasks(todos);\r\n        setTodoStats(stats);\r\n      } catch (error) {\r\n        console.error('获取TODO数据时发生未知错误:', error);\r\n        setError('获取TODO数据失败，请刷新页面重试');\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchTodoData();\r\n  }, []);\r\n\r\n  // 根据激活的标签和搜索文本过滤任务\r\n  const filteredPersonalTasks = personalTasks.filter((task) => {\r\n    // 根据标签过滤\r\n    if (activeTab === \"pending\" && task.status === 1) return false;\r\n    if (activeTab === \"completed\" && task.status === 0) return false;\r\n\r\n    // 根据搜索文本过滤\r\n    if (\r\n      searchText &&\r\n      !task.title.toLowerCase().includes(searchText.toLowerCase())\r\n    ) {\r\n      return false;\r\n    }\r\n\r\n    return true;\r\n  });\r\n\r\n  // 处理待办事项操作\r\n  const handleToggleTodoStatus = async (id: number) => {\r\n    try {\r\n      const task = personalTasks.find(t => t.id === id);\r\n      if (!task) {\r\n        message.error('任务不存在');\r\n        return;\r\n      }\r\n\r\n      const newStatus = task.status === 0 ? 1 : 0;\r\n      console.log(`TodoManagement: 更新任务状态 ${id} -> ${newStatus}`);\r\n\r\n      await TodoService.updateTodo(id, { status: newStatus });\r\n\r\n      // 更新本地状态\r\n      setPersonalTasks(\r\n        personalTasks.map((task) =>\r\n          task.id === id ? { ...task, status: newStatus } : task\r\n        )\r\n      );\r\n\r\n      // 刷新统计数据\r\n      try {\r\n        const stats = await TodoService.getTodoStats();\r\n        setTodoStats(stats);\r\n      } catch (statsError) {\r\n        console.error('刷新统计数据失败:', statsError);\r\n        // 统计数据刷新失败不影响主要操作\r\n      }\r\n\r\n      message.success(newStatus === 1 ? '任务已完成' : '任务已标记为未完成');\r\n    } catch (error) {\r\n      console.error('更新任务状态失败:', error);\r\n      message.error('更新任务状态失败，请稍后重试');\r\n    }\r\n  };\r\n\r\n  const handleAddOrUpdateTodo = async (values: any) => {\r\n    try {\r\n      console.log('TodoManagement: 保存任务', { editingTodoId, values });\r\n\r\n      if (editingTodoId) {\r\n        // 更新现有待办事项\r\n        const updatedTodo = await TodoService.updateTodo(editingTodoId, {\r\n          title: values.name,\r\n          priority: values.priority\r\n        });\r\n\r\n        setPersonalTasks(\r\n          personalTasks.map((task) =>\r\n            task.id === editingTodoId ? updatedTodo : task\r\n          )\r\n        );\r\n        message.success('任务更新成功');\r\n      } else {\r\n        // 添加新待办事项\r\n        const newTodo = await TodoService.createTodo({\r\n          title: values.name,\r\n          priority: values.priority\r\n        });\r\n\r\n        setPersonalTasks([newTodo, ...personalTasks]);\r\n        message.success('任务创建成功');\r\n      }\r\n\r\n      // 刷新统计数据\r\n      try {\r\n        const stats = await TodoService.getTodoStats();\r\n        setTodoStats(stats);\r\n      } catch (statsError) {\r\n        console.error('刷新统计数据失败:', statsError);\r\n        // 统计数据刷新失败不影响主要操作\r\n      }\r\n\r\n      // 重置表单并关闭模态框\r\n      setTodoModalVisible(false);\r\n      setEditingTodoId(null);\r\n      todoForm.resetFields();\r\n    } catch (error) {\r\n      console.error('保存任务失败:', error);\r\n      const action = editingTodoId ? '更新' : '创建';\r\n      message.error(`${action}任务失败，请检查网络连接后重试`);\r\n    }\r\n  };\r\n\r\n  const handleDeleteTodo = async (id: number) => {\r\n    try {\r\n      console.log('TodoManagement: 删除任务', id);\r\n\r\n      await TodoService.deleteTodo(id);\r\n      setPersonalTasks(personalTasks.filter((task) => task.id !== id));\r\n\r\n      // 刷新统计数据\r\n      try {\r\n        const stats = await TodoService.getTodoStats();\r\n        setTodoStats(stats);\r\n      } catch (statsError) {\r\n        console.error('刷新统计数据失败:', statsError);\r\n        // 统计数据刷新失败不影响主要操作\r\n      }\r\n\r\n      message.success('任务删除成功');\r\n    } catch (error) {\r\n      console.error('删除任务失败:', error);\r\n      message.error('删除任务失败，请稍后重试');\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Card\r\n      className=\"dashboard-card\"\r\n      style={{\r\n        borderRadius: 12,\r\n        boxShadow: \"0 4px 12px rgba(0,0,0,0.05)\",\r\n        border: \"none\",\r\n        background: \"linear-gradient(145deg, #ffffff, #f5f8ff)\",\r\n      }}\r\n      title={\r\n        <Flex justify=\"space-between\" align=\"center\">\r\n          <Text strong>待办事项</Text>\r\n        </Flex>\r\n      }\r\n    >\r\n      {/* 响应式标题行：搜索框、新增按钮、优先级计数和完成率 */}\r\n      <div\r\n        style={{\r\n          marginBottom: 16,\r\n          padding: \"12px 16px\",\r\n          background: \"#fafbfc\",\r\n          borderRadius: 8,\r\n          border: \"1px solid #f0f0f0\"\r\n        }}\r\n      >\r\n        {/* 使用 Row/Col 实现三列响应式布局 */}\r\n        <Row gutter={[16, 12]} align=\"middle\">\r\n          {/* 第一列：搜索框和新增按钮 */}\r\n          <Col xs={24} sm={24} md={8} lg={8} xl={8}>\r\n            <Flex align=\"center\" gap={12} style={{ width: \"100%\" }}>\r\n              <Input.Search\r\n                placeholder=\"搜索任务...\"\r\n                allowClear\r\n                prefix={<SearchOutlined />}\r\n                value={searchText}\r\n                onChange={(e) => setSearchText(e.target.value)}\r\n                style={{ flex: 1 }}\r\n                size=\"middle\"\r\n              />\r\n\r\n              <Button\r\n                type=\"primary\"\r\n                icon={<PlusOutlined />}\r\n                onClick={() => {\r\n                  setEditingTodoId(null);\r\n                  todoForm.resetFields();\r\n                  setTodoModalVisible(true);\r\n                }}\r\n                style={{\r\n                  background: \"#1890ff\",\r\n                  borderColor: \"#1890ff\",\r\n                  boxShadow: \"0 2px 4px rgba(24, 144, 255, 0.3)\",\r\n                  fontWeight: 500,\r\n                  minWidth: 80\r\n                }}\r\n                size=\"middle\"\r\n              >\r\n                新增\r\n              </Button>\r\n            </Flex>\r\n          </Col>\r\n\r\n          {/* 第二列：各优先级数量 */}\r\n          <Col xs={24} sm={24} md={8} lg={8} xl={8}>\r\n            <Flex\r\n              align=\"center\"\r\n              justify=\"center\"\r\n              wrap=\"wrap\"\r\n            >\r\n              <Space size={12} wrap>\r\n                <Tooltip title={`高优先级任务: ${todoStats.highPriorityCount}个`}>\r\n                  <Flex align=\"center\" gap={4}>\r\n                    <div\r\n                      style={{\r\n                        width: 8,\r\n                        height: 8,\r\n                        borderRadius: \"50%\",\r\n                        background: \"#ff4d4f\",\r\n                      }}\r\n                    />\r\n                    <Text style={{ fontSize: 12, fontWeight: 500, color: \"#262626\" }}>\r\n                      高: {todoStats.highPriorityCount}\r\n                    </Text>\r\n                  </Flex>\r\n                </Tooltip>\r\n\r\n                <Tooltip title={`中优先级任务: ${todoStats.mediumPriorityCount}个`}>\r\n                  <Flex align=\"center\" gap={4}>\r\n                    <div\r\n                      style={{\r\n                        width: 8,\r\n                        height: 8,\r\n                        borderRadius: \"50%\",\r\n                        background: \"#faad14\",\r\n                      }}\r\n                    />\r\n                    <Text style={{ fontSize: 12, fontWeight: 500, color: \"#262626\" }}>\r\n                      中: {todoStats.mediumPriorityCount}\r\n                    </Text>\r\n                  </Flex>\r\n                </Tooltip>\r\n\r\n                <Tooltip title={`低优先级任务: ${todoStats.lowPriorityCount}个`}>\r\n                  <Flex align=\"center\" gap={4}>\r\n                    <div\r\n                      style={{\r\n                        width: 8,\r\n                        height: 8,\r\n                        borderRadius: \"50%\",\r\n                        background: \"#52c41a\",\r\n                      }}\r\n                    />\r\n                    <Text style={{ fontSize: 12, fontWeight: 500, color: \"#262626\" }}>\r\n                      低: {todoStats.lowPriorityCount}\r\n                    </Text>\r\n                  </Flex>\r\n                </Tooltip>\r\n              </Space>\r\n            </Flex>\r\n          </Col>\r\n\r\n          {/* 第三列：完成率 */}\r\n          <Col xs={24} sm={24} md={8} lg={8} xl={8}>\r\n            <Flex\r\n              align=\"center\"\r\n              justify=\"center\"\r\n            >\r\n              <Tooltip title={`完成率: ${todoStats.completionPercentage}% (${todoStats.completedCount}/${todoStats.totalCount})`}>\r\n                <Flex align=\"center\" gap={6}>\r\n                  <Text style={{ fontSize: 12, fontWeight: 500, color: \"#595959\" }}>\r\n                    完成率:\r\n                  </Text>\r\n                  <Progress\r\n                    percent={todoStats.completionPercentage}\r\n                    size=\"small\"\r\n                    style={{ width: 80 }}\r\n                    strokeColor=\"#52c41a\"\r\n                    showInfo={false}\r\n                  />\r\n                  <Text style={{ fontSize: 12, fontWeight: 600, color: \"#262626\" }}>\r\n                    {todoStats.completionPercentage}%\r\n                  </Text>\r\n                </Flex>\r\n              </Tooltip>\r\n            </Flex>\r\n          </Col>\r\n        </Row>\r\n      </div>\r\n\r\n      {/* 第二行：标签页 */}\r\n      <Tabs\r\n        activeKey={activeTab}\r\n        onChange={(key) =>\r\n          setActiveTab(key as \"all\" | \"pending\" | \"completed\")\r\n        }\r\n        size=\"middle\"\r\n        style={{ marginBottom: 8 }}\r\n      >\r\n        <TabPane tab=\"全部\" key=\"all\" />\r\n        <TabPane tab=\"待处理\" key=\"pending\" />\r\n        <TabPane tab=\"已完成\" key=\"completed\" />\r\n      </Tabs>\r\n\r\n      {/* 待办事项列表 */}\r\n      {error ? (\r\n        <Alert\r\n          message=\"TODO数据加载失败\"\r\n          description={error}\r\n          type=\"error\"\r\n          showIcon\r\n          style={{ marginBottom: 16 }}\r\n        />\r\n      ) : (\r\n        <Spin spinning={loading}>\r\n          <List\r\n            dataSource={filteredPersonalTasks}\r\n            renderItem={(item) => {\r\n          return (\r\n            <List.Item\r\n              className=\"todo-item\"\r\n              style={{\r\n                padding: \"10px 16px\",\r\n                marginBottom: 12,\r\n                borderRadius: 8,\r\n                background: \"#fff\",\r\n                opacity: item.status === 1 ? 0.7 : 1,\r\n                borderLeft: `3px solid ${\r\n                  item.status === 1\r\n                    ? \"#52c41a\"\r\n                    : item.priority === 3\r\n                    ? \"#ff4d4f\"\r\n                    : item.priority === 2\r\n                    ? \"#faad14\"\r\n                    : \"#8c8c8c\"\r\n                }`,\r\n                boxShadow: \"0 1px 4px rgba(0,0,0,0.05)\",\r\n              }}\r\n            >\r\n              <Flex align=\"center\" gap={12} style={{ width: \"100%\" }}>\r\n                {/* 左侧状态和优先级指示器 */}\r\n                <Flex vertical align=\"center\">\r\n                  {item.status === 1 ? (\r\n                    <Flex\r\n                      align=\"center\"\r\n                      justify=\"center\"\r\n                      style={{\r\n                        width: 22,\r\n                        height: 22,\r\n                        borderRadius: \"50%\",\r\n                        background: \"#52c41a\",\r\n                      }}\r\n                    >\r\n                      <CheckOutlined\r\n                        style={{ color: \"#fff\", fontSize: 12 }}\r\n                      />\r\n                    </Flex>\r\n                  ) : (\r\n                    <div\r\n                      style={{\r\n                        width: 18,\r\n                        height: 18,\r\n                        borderRadius: \"50%\",\r\n                        border: `2px solid ${\r\n                          item.priority === 3\r\n                            ? \"#ff4d4f\"\r\n                            : item.priority === 2\r\n                            ? \"#faad14\"\r\n                            : \"#8c8c8c\"\r\n                        }`,\r\n                      }}\r\n                    />\r\n                  )}\r\n\r\n                  <div\r\n                    style={{\r\n                      width: 2,\r\n                      height: 24,\r\n                      background: \"#f0f0f0\",\r\n                      marginTop: 4,\r\n                    }}\r\n                  />\r\n                </Flex>\r\n\r\n                {/* 任务信息区 */}\r\n                <Flex vertical style={{ flex: 1 }}>\r\n                  <Text\r\n                    style={{\r\n                      fontSize: 14,\r\n                      fontWeight:\r\n                        item.priority === 3 ? 500 : \"normal\",\r\n                      textDecoration: item.status === 1\r\n                        ? \"line-through\"\r\n                        : \"none\",\r\n                      color: item.status === 1 ? \"#8c8c8c\" : \"#262626\",\r\n                    }}\r\n                  >\r\n                    {item.title}\r\n                  </Text>\r\n\r\n                  {/* 显示创建日期 */}\r\n                  <Space align=\"center\" size={6} style={{ marginTop: 4 }}>\r\n                    <CalendarOutlined\r\n                      style={{\r\n                        fontSize: 12,\r\n                        color: \"#8c8c8c\",\r\n                      }}\r\n                    />\r\n                    <Text type=\"secondary\" style={{ fontSize: 12 }}>\r\n                      创建于: {new Date(item.createdAt).toLocaleDateString('zh-CN')}\r\n                    </Text>\r\n                  </Space>\r\n                </Flex>\r\n\r\n                {/* 操作按钮区 */}\r\n                <Dropdown\r\n                  trigger={['click']}\r\n                  menu={{\r\n                    items: [\r\n                      {\r\n                        key: 'complete',\r\n                        label: item.status === 1 ? '标记未完成' : '标记完成',\r\n                        icon: (\r\n                          <CheckOutlined\r\n                            style={{\r\n                              color: item.status === 1 ? '#8c8c8c' : '#52c41a',\r\n                              fontSize: 14 \r\n                            }} \r\n                          />\r\n                        )\r\n                      },\r\n                      {\r\n                        key: 'edit',\r\n                        label: '编辑任务',\r\n                        icon: <EditOutlined style={{ color: '#8c8c8c' }} />\r\n                      },\r\n                      {\r\n                        key: 'delete',\r\n                        label: '删除任务',\r\n                        icon: <DeleteOutlined style={{ color: '#ff4d4f' }} />,\r\n                        danger: true\r\n                      }\r\n                    ],\r\n                    onClick: ({ key }) => {\r\n                      if (key === \"complete\") {\r\n                        handleToggleTodoStatus(item.id);\r\n                      } else if (key === \"edit\") {\r\n                        setEditingTodoId(item.id);\r\n                        todoForm.setFieldsValue({\r\n                          name: item.title,\r\n                          priority: item.priority\r\n                        });\r\n                        setTodoModalVisible(true);\r\n                      } else if (key === \"delete\") {\r\n                        handleDeleteTodo(item.id);\r\n                      }\r\n                    }\r\n                  }}\r\n                >\r\n                  <Button \r\n                    type=\"text\" \r\n                    size=\"small\" \r\n                    icon={<MoreOutlined />} \r\n                    style={{ width: 32, height: 32 }} \r\n                  />\r\n                </Dropdown>\r\n              </Flex>\r\n            </List.Item>\r\n          );\r\n        }}\r\n      />\r\n\r\n      {/* 待办事项表单模态框 */}\r\n      <Modal\r\n        title={editingTodoId ? \"编辑待办事项\" : \"新增待办事项\"}\r\n        open={todoModalVisible}\r\n        onCancel={() => {\r\n          setTodoModalVisible(false);\r\n          todoForm.resetFields();\r\n        }}\r\n        onOk={() => {\r\n          todoForm.submit();\r\n        }}\r\n        centered\r\n        destroyOnClose\r\n        footer={[\r\n          <Button key=\"cancel\" onClick={() => setTodoModalVisible(false)}>\r\n            取消\r\n          </Button>,\r\n          <Button\r\n            key=\"submit\"\r\n            type=\"primary\"\r\n            onClick={() => {\r\n              todoForm.submit();\r\n            }}\r\n            style={{\r\n              background: \"#1890ff\",\r\n              borderColor: \"#1890ff\",\r\n              boxShadow: \"0 2px 4px rgba(24, 144, 255, 0.3)\",\r\n            }}\r\n          >\r\n            {editingTodoId ? \"更新任务\" : \"创建任务\"}\r\n          </Button>,\r\n        ]}\r\n      >\r\n        <Form\r\n          form={todoForm}\r\n          layout=\"vertical\"\r\n          onFinish={handleAddOrUpdateTodo}\r\n          autoComplete=\"off\"\r\n        >\r\n          <Form.Item\r\n            name=\"name\"\r\n            label=\"任务名称\"\r\n            rules={[{ required: true, message: \"请输入任务名称\" }]}\r\n          >\r\n            <Input\r\n              placeholder=\"请输入任务名称\"\r\n              size=\"large\"\r\n              style={{ borderRadius: 6 }}\r\n            />\r\n          </Form.Item>\r\n\r\n          <Form.Item\r\n            name=\"priority\"\r\n            label=\"优先级\"\r\n            initialValue={2}\r\n            rules={[{ required: true, message: \"请选择优先级\" }]}\r\n          >\r\n            <Select\r\n              size=\"large\"\r\n              options={[\r\n                { value: 3, label: \"高优先级\" },\r\n                { value: 2, label: \"中优先级\" },\r\n                { value: 1, label: \"低优先级\" },\r\n              ]}\r\n              style={{ borderRadius: 6 }}\r\n            />\r\n          </Form.Item>\r\n        </Form>\r\n      </Modal>\r\n        </Spin>\r\n      )}\r\n    </Card>\r\n  );\r\n};\r\n\r\nexport default TodoManagement;", "import {\r\n  Bar<PERSON>hartOutlined,\r\n  MailOutlined,\r\n  PhoneOutlined,\r\n  UserOutlined,\r\n} from \"@ant-design/icons\";\r\nimport {\r\n  Card,\r\n  Flex,\r\n  Space,\r\n  Typography,\r\n  Spin,\r\n  Alert,\r\n  Avatar,\r\n  Row,\r\n  Col,\r\n} from \"antd\";\r\nimport React, { useState, useEffect } from \"react\";\r\nimport { UserService } from \"@/services/user\";\r\nimport type { UserPersonalStatsResponse, UserProfileDetailResponse } from \"@/types/api\";\r\n \r\nconst { Title, Text } = Typography;\r\n\r\nconst UserProfileCard: React.FC = () => {\r\n  // 用户详细信息状态\r\n  const [userInfo, setUserInfo] = useState<UserProfileDetailResponse>({\r\n    name: \"\",\r\n    position: \"\",\r\n    email: \"\",\r\n    telephone: \"\",\r\n    registerDate: \"\",\r\n    lastLoginTime: \"\",\r\n    lastLoginTeam: \"\",\r\n    teamCount: 0,\r\n    avatar: \"\",\r\n  });\r\n  const [userInfoLoading, setUserInfoLoading] = useState(true);\r\n  const [userInfoError, setUserInfoError] = useState<string | null>(null);\r\n\r\n  // 个人统计数据状态\r\n  const [personalStats, setPersonalStats] = useState<UserPersonalStatsResponse>({\r\n    vehicles: 0,\r\n    personnel: 0,\r\n    warnings: 0,\r\n    alerts: 0,\r\n  });\r\n  const [statsLoading, setStatsLoading] = useState(true);\r\n  const [statsError, setStatsError] = useState<string | null>(null);\r\n\r\n\r\n\r\n  // 状态管理\r\n\r\n  // 获取用户数据\r\n  useEffect(() => {\r\n    console.log('UserProfileCard: useEffect 开始执行');\r\n\r\n    const fetchUserData = async () => {\r\n      try {\r\n        console.log('UserProfileCard: 开始获取用户数据');\r\n\r\n        // 分别获取用户详细信息和统计数据，避免一个失败影响另一个\r\n        const userDetailPromise = UserService.getUserProfileDetail().catch(error => {\r\n          console.error('获取用户详细信息失败:', error);\r\n          setUserInfoError('获取用户详细信息失败，请稍后重试');\r\n          return null;\r\n        });\r\n\r\n        const statsPromise = UserService.getUserPersonalStats().catch(error => {\r\n          console.error('获取统计数据失败:', error);\r\n          setStatsError('获取统计数据失败，请稍后重试');\r\n          return null;\r\n        });\r\n\r\n        const [userDetail, stats] = await Promise.all([userDetailPromise, statsPromise]);\r\n\r\n        if (userDetail) {\r\n          console.log('UserProfileCard: 获取到用户详细信息:', userDetail);\r\n          setUserInfo(userDetail);\r\n          setUserInfoError(null);\r\n        }\r\n\r\n        if (stats) {\r\n          console.log('UserProfileCard: 获取到统计数据:', stats);\r\n          setPersonalStats(stats);\r\n          setStatsError(null);\r\n        }\r\n\r\n      } catch (error) {\r\n        console.error('获取用户数据时发生未知错误:', error);\r\n        setUserInfoError('获取用户数据失败，请刷新页面重试');\r\n        setStatsError('获取统计数据失败，请刷新页面重试');\r\n      } finally {\r\n        setUserInfoLoading(false);\r\n        setStatsLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchUserData();\r\n  }, []);\r\n\r\n\r\n\r\n  return (\r\n    <>\r\n      {/* 用户信息主卡片 */}\r\n      {userInfoError ? (\r\n        <Alert\r\n          message=\"用户信息加载失败\"\r\n          description={userInfoError}\r\n          type=\"error\"\r\n          showIcon\r\n          style={{ marginBottom: 24 }}\r\n        />\r\n      ) : (\r\n        <Spin spinning={userInfoLoading}>\r\n          {/* 使用 Card 组件替代自定义 div */}\r\n          <Card\r\n            style={{\r\n              background: \"linear-gradient(135deg, #667eea 0%, #764ba2 100%)\",\r\n              borderRadius: 16,\r\n              color: \"white\",\r\n              position: \"relative\",\r\n              overflow: \"hidden\",\r\n              minHeight: 140, // 改为最小高度，允许内容撑开\r\n              border: \"none\",\r\n            }}\r\n            styles={{\r\n              body: {\r\n                padding: \"16px 16px 16px 16px\", // 统一使用16px内边距\r\n                height: \"100%\"\r\n              }\r\n            }}\r\n          >\r\n\r\n              {/* 装饰性背景元素 */}\r\n              <div\r\n                style={{\r\n                  position: \"absolute\",\r\n                  top: -25,\r\n                  right: -25,\r\n                  width: 100,\r\n                  height: 100,\r\n                  background: \"rgba(255,255,255,0.1)\",\r\n                  borderRadius: \"50%\",\r\n                }}\r\n              />\r\n              <div\r\n                style={{\r\n                  position: \"absolute\",\r\n                  bottom: -30,\r\n                  left: -30,\r\n                  width: 80,\r\n                  height: 80,\r\n                  background: \"rgba(255,255,255,0.05)\",\r\n                  borderRadius: \"50%\",\r\n                }}\r\n              />\r\n              <div\r\n                style={{\r\n                  position: \"absolute\",\r\n                  top: \"50%\",\r\n                  right: \"20%\",\r\n                  width: 60,\r\n                  height: 60,\r\n                  background: \"rgba(255,255,255,0.03)\",\r\n                  borderRadius: \"50%\",\r\n                  transform: \"translateY(-50%)\",\r\n                }}\r\n              />\r\n\r\n              {/* 主要内容区域 - 使用响应式网格布局 */}\r\n              <Row\r\n                gutter={[16, 12]}\r\n                align=\"middle\"\r\n                style={{ position: \"relative\", zIndex: 1, width: \"100%\", minHeight: \"100%\" }}\r\n              >\r\n                {/* 第一列：用户基本信息区域 */}\r\n                <Col xs={24} sm={24} md={8} lg={7} xl={6}>\r\n                  <Flex align=\"center\" style={{ minHeight: \"80px\" }}>\r\n                    {/* 用户头像 - 使用 Ant Design Avatar 组件 */}\r\n                    <Avatar\r\n                      size={64}\r\n                      shape=\"square\"\r\n                      style={{\r\n                        backgroundColor: \"rgba(255,255,255,0.2)\",\r\n                        marginRight: 20,\r\n                        fontSize: 24,\r\n                        fontWeight: 600,\r\n                        border: \"2px solid rgba(255,255,255,0.3)\",\r\n                      }}\r\n                    >\r\n                      {userInfo.name ? userInfo.name.charAt(0).toUpperCase() : <UserOutlined />}\r\n                    </Avatar>\r\n\r\n                    {/* 用户信息 - 使用 Space 组件垂直布局 */}\r\n                    <Space direction=\"vertical\" size={4}>\r\n                      <Title\r\n                        level={3}\r\n                        style={{\r\n                          margin: 0,\r\n                          color: \"white\",\r\n                          fontSize: 22,\r\n                          fontWeight: 600,\r\n                        }}\r\n                      >\r\n                        {userInfo.name || \"加载中...\"}\r\n                      </Title>\r\n\r\n                      {/* 联系信息 - 使用 Space 组件垂直排列 */}\r\n                      <Space direction=\"vertical\" size={4}>\r\n                        {userInfo.email && (\r\n                          <Space size={6} align=\"center\">\r\n                            <MailOutlined style={{ fontSize: 13, color: \"rgba(255,255,255,0.9)\" }} />\r\n                            <Text style={{ color: \"rgba(255,255,255,0.9)\", fontSize: 12 }}>\r\n                              {userInfo.email}\r\n                            </Text>\r\n                          </Space>\r\n                        )}\r\n                        {userInfo.telephone && (\r\n                          <Space size={6} align=\"center\">\r\n                            <PhoneOutlined style={{ fontSize: 13, color: \"rgba(255,255,255,0.9)\" }} />\r\n                            <Text style={{ color: \"rgba(255,255,255,0.9)\", fontSize: 12 }}>\r\n                              {userInfo.telephone}\r\n                            </Text>\r\n                          </Space>\r\n                        )}\r\n                      </Space>\r\n\r\n                      {/* 注册日期 */}\r\n                      {userInfo.registerDate && (\r\n                        <Text style={{ fontSize: 13, color: \"rgba(255,255,255,0.8)\", fontWeight: 500 }}>\r\n                          注册于 {userInfo.registerDate}\r\n                        </Text>\r\n                      )}\r\n                    </Space>\r\n                  </Flex>\r\n                </Col>\r\n\r\n                {/* 第二列：数据概览区域 - 两行结构 */}\r\n                <Col xs={24} sm={24} md={8} lg={10} xl={12}>\r\n                  <Flex\r\n                    vertical\r\n                    justify=\"center\"\r\n                    style={{ minHeight: \"80px\", textAlign: \"center\", padding: \"8px 0\" }}\r\n                  >\r\n                    {/* 第一行：数据概览标题和图标 */}\r\n                    <Space\r\n                      align=\"center\"\r\n                      style={{\r\n                        justifyContent: \"center\",\r\n                        marginBottom: 16,\r\n                      }}\r\n                    >\r\n                      <BarChartOutlined\r\n                        style={{\r\n                          fontSize: 16,\r\n                          color: \"rgba(255,255,255,0.9)\",\r\n                        }}\r\n                      />\r\n                      <Text style={{ color: \"rgba(255,255,255,0.9)\", fontSize: 14, fontWeight: 600 }}>\r\n                        数据概览\r\n                      </Text>\r\n                    </Space>\r\n\r\n                    {/* 第二行：指标卡片 */}\r\n                    {statsError ? (\r\n                      <Text style={{ fontSize: 12, color: \"rgba(255,255,255,0.8)\" }}>\r\n                        数据加载失败\r\n                      </Text>\r\n                    ) : (\r\n                      <Spin spinning={statsLoading}>\r\n                        <Row gutter={[4, 8]} justify=\"center\">\r\n                          <Col xs={6} sm={6} md={6} lg={6} xl={6}>\r\n                            <div style={{ textAlign: 'center' }}>\r\n                              <div style={{\r\n                                fontSize: 16,\r\n                                fontWeight: 700,\r\n                                color: \"white\",\r\n                                lineHeight: 1,\r\n                              }}>\r\n                                {personalStats.vehicles}\r\n                              </div>\r\n                              <div style={{\r\n                                fontSize: 11,\r\n                                color: \"rgba(255,255,255,0.8)\",\r\n                                marginTop: 2,\r\n                              }}>\r\n                                车辆\r\n                              </div>\r\n                            </div>\r\n                          </Col>\r\n                          <Col xs={6} sm={6} md={6} lg={6} xl={6}>\r\n                            <div style={{ textAlign: 'center' }}>\r\n                              <div style={{\r\n                                fontSize: 16,\r\n                                fontWeight: 700,\r\n                                color: \"white\",\r\n                                lineHeight: 1,\r\n                              }}>\r\n                                {personalStats.personnel}\r\n                              </div>\r\n                              <div style={{\r\n                                fontSize: 11,\r\n                                color: \"rgba(255,255,255,0.8)\",\r\n                                marginTop: 2,\r\n                              }}>\r\n                                人员\r\n                              </div>\r\n                            </div>\r\n                          </Col>\r\n                          <Col xs={6} sm={6} md={6} lg={6} xl={6}>\r\n                            <div style={{ textAlign: 'center' }}>\r\n                              <div style={{\r\n                                fontSize: 16,\r\n                                fontWeight: 700,\r\n                                color: \"white\",\r\n                                lineHeight: 1,\r\n                              }}>\r\n                                {personalStats.warnings}\r\n                              </div>\r\n                              <div style={{\r\n                                fontSize: 11,\r\n                                color: \"rgba(255,255,255,0.8)\",\r\n                                marginTop: 2,\r\n                              }}>\r\n                                预警\r\n                              </div>\r\n                            </div>\r\n                          </Col>\r\n                          <Col xs={6} sm={6} md={6} lg={6} xl={6}>\r\n                            <div style={{ textAlign: 'center' }}>\r\n                              <div style={{\r\n                                fontSize: 16,\r\n                                fontWeight: 700,\r\n                                color: \"white\",\r\n                                lineHeight: 1,\r\n                              }}>\r\n                                {personalStats.alerts}\r\n                              </div>\r\n                              <div style={{\r\n                                fontSize: 11,\r\n                                color: \"rgba(255,255,255,0.8)\",\r\n                                marginTop: 2,\r\n                              }}>\r\n                                告警\r\n                              </div>\r\n                            </div>\r\n                          </Col>\r\n                        </Row>\r\n                      </Spin>\r\n                    )}\r\n                  </Flex>\r\n                </Col>\r\n\r\n                {/* 第三列：最近活动信息 */}\r\n                <Col xs={24} sm={24} md={8} lg={7} xl={6}>\r\n                  <Flex\r\n                    vertical\r\n                    justify=\"center\"\r\n                    style={{ minHeight: \"80px\", padding: \"8px 0\" }}\r\n                  >\r\n                    <Space direction=\"vertical\" size={10}>\r\n                      <Space direction=\"vertical\" size={4}>\r\n                        <Text style={{ fontSize: 12, color: \"rgba(255,255,255,0.8)\", fontWeight: 500 }}>\r\n                          最后登录时间\r\n                        </Text>\r\n                        <Text style={{ fontSize: 14, color: \"white\", fontWeight: 600, lineHeight: 1.3 }}>\r\n                          {userInfo.lastLoginTime || \"暂无记录\"}\r\n                        </Text>\r\n                      </Space>\r\n                      <Space direction=\"vertical\" size={4}>\r\n                        <Text style={{ fontSize: 12, color: \"rgba(255,255,255,0.8)\", fontWeight: 500 }}>\r\n                          最后登录团队\r\n                        </Text>\r\n                        <Text style={{ fontSize: 14, color: \"white\", fontWeight: 600, lineHeight: 1.3 }}>\r\n                          {userInfo.lastLoginTeam || \"暂无记录\"}\r\n                        </Text>\r\n                      </Space>\r\n                    </Space>\r\n                  </Flex>\r\n                </Col>\r\n              </Row>\r\n            </Card>\r\n          </Spin>\r\n        )}\r\n    </>\r\n  );\r\n};\r\n\r\nexport default UserProfileCard;", "import React from \"react\";\nimport { Card, Col, Row, Spin } from \"antd\";\nimport { useModel } from '@umijs/max';\n\nimport TodoManagement from './TodoManagement';\nimport TeamListCard from './TeamListCard';\nimport UserProfileCard from \"./UserProfileCard\";\nimport UserFloatButton from '@/components/FloatButton';\n\nconst PersonalCenterPage: React.FC = () => {\n  const { initialState, loading } = useModel('@@initialState');\n\n  // 如果正在加载，显示加载状态\n  if (loading) {\n    return (\n      <div style={{\n        minHeight: \"100vh\",\n        background: \"#f5f8ff\",\n        display: \"flex\",\n        justifyContent: \"center\",\n        alignItems: \"center\"\n      }}>\n        <Spin size=\"large\" />\n        <div style={{ marginLeft: 16 }}>正在加载用户信息...</div>\n      </div>\n    );\n  }\n\n  // 如果用户未登录，显示错误信息\n  if (!initialState?.currentUser) {\n    return (\n      <div style={{\n        minHeight: \"100vh\",\n        background: \"#f5f8ff\",\n        display: \"flex\",\n        justifyContent: \"center\",\n        alignItems: \"center\"\n      }}>\n        <div>用户未登录，请先登录</div>\n      </div>\n    );\n  }\n\n  return (\n    <div style={{\n      minHeight: \"100vh\",\n      background: \"#f5f8ff\",\n      padding: \"12px 12px 24px 12px\" // 移动端减少左右边距\n    }}>\n      {/* 大的容器区域 */}\n      <Card\n        style={{\n          width: \"100%\",\n          minHeight: \"calc(100vh - 48px)\",\n          borderRadius: \"12px\",\n          boxShadow: \"0 4px 12px rgba(0, 0, 0, 0.1)\"\n        }}\n        styles={{\n          body: {\n            padding: \"12px\", // 移动端减少内边距\n            \"@media (min-width: 768px)\": {\n              padding: \"24px\"\n            }\n          }\n        }}\n      >\n        <Row gutter={[16, 16]} style={{ margin: 0 }}>\n          {/* 个人信息卡片 - 全宽显示 */}\n          <Col xs={24} style={{ marginBottom: 8 }}>\n            <UserProfileCard />\n          </Col>\n\n          {/* 待办事项 - 响应式布局 */}\n          <Col\n            xs={24}\n            sm={24}\n            md={24}\n            lg={12}\n            xl={12}\n            xxl={12}\n            style={{ marginBottom: { xs: 8, lg: 0 } }}\n          >\n            <TodoManagement />\n          </Col>\n\n          {/* 团队列表 - 响应式布局 */}\n          <Col\n            xs={24}\n            sm={24}\n            md={24}\n            lg={12}\n            xl={12}\n            xxl={12}\n          >\n            <TeamListCard />\n          </Col>\n        </Row>\n      </Card>\n\n      {/* 添加浮动按钮 */}\n      <UserFloatButton />\n    </div>\n  );\n};\n\nexport default PersonalCenterPage;", "/**\n * TODO服务\n */\n\nimport { apiRequest } from '@/utils/request';\nimport type {\n  TodoResponse,\n  CreateTodoRequest,\n  UpdateTodoRequest,\n  TodoStatsResponse,\n} from '@/types/api';\n\nexport class TodoService {\n  /**\n   * 获取用户的TODO列表\n   */\n  static async getUserTodos(): Promise<TodoResponse[]> {\n    const response = await apiRequest.get<TodoResponse[]>('/todos');\n    return response.data;\n  }\n\n  /**\n   * 创建TODO\n   */\n  static async createTodo(request: CreateTodoRequest): Promise<TodoResponse> {\n    const response = await apiRequest.post<TodoResponse>('/todos', request);\n    return response.data;\n  }\n\n  /**\n   * 更新TODO\n   */\n  static async updateTodo(id: number, request: UpdateTodoRequest): Promise<TodoResponse> {\n    const response = await apiRequest.put<TodoResponse>(`/todos/${id}`, request);\n    return response.data;\n  }\n\n  /**\n   * 删除TODO\n   */\n  static async deleteTodo(id: number): Promise<void> {\n    await apiRequest.delete(`/todos/${id}`);\n  }\n\n  /**\n   * 获取TODO统计信息\n   */\n  static async getTodoStats(): Promise<TodoStatsResponse> {\n    const response = await apiRequest.get<TodoStatsResponse>('/todos/stats');\n    return response.data;\n  }\n}\n"], "names": [], "mappings": ";;;;;;;4BAucA;;;eAAA;;;;;;wEAvc2C;6BAYpC;6BACqB;iCACA;4BACM;8BAU3B;;;;;;;;;;AAEP,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,gBAAU;AAElC,UAAU;AACV,MAAM,SAAS,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2ChB,CAAC;AAED,MAAM,eAAyB;;IAC7B,WAAW;IACX,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,eAAQ,EAAuB,EAAE;IAC3D,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAC;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,eAAQ,EAAgB;IAClD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,IAAA,eAAQ,EAAgB;IAEtE,MAAM,EAAE,YAAY,EAAE,eAAe,EAAE,GAAG,IAAA,aAAQ,EAAC;IACnD,MAAM,cAAc,yBAAA,mCAAA,aAAc,WAAW;IAE7C,WAAW;IACX,IAAA,gBAAS,EAAC;QACR,MAAM,aAAa;YACjB,IAAI;gBACF,WAAW;gBACX,SAAS;gBACT,MAAM,YAAY,MAAM,iBAAW,CAAC,qBAAqB;gBACzD,SAAS;YACX,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,aAAa;gBAC3B,SAAS;YACX,SAAU;gBACR,WAAW;YACb;QACF;QAEA;IACF,GAAG,EAAE;IAEL,WAAW;IACX,MAAM,mBAAmB,OAAO,QAAgB;QAC9C,IAAI;YACF,mBAAmB;YAEnB,8BAA8B;YAC9B,IAAI,YAAW,wBAAA,kCAAA,YAAa,EAAE,GAAE;gBAC9B,aAAO,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,SAAS,CAAC;gBAClC,YAAO,CAAC,IAAI,CAAC;gBACb;YACF;YAEA,eAAe;YACf,MAAM,WAAW,MAAM,qBAAW,CAAC,UAAU,CAAC;gBAAE;YAAO;YAEvD,kBAAkB;YAClB,IAAI,SAAS,oBAAoB,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,EAAE,KAAK,QAAQ;gBACjF,aAAO,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,SAAS,CAAC;gBAEpC,+BAA+B;gBAC/B,IAAI,CAAA,yBAAA,mCAAA,aAAc,aAAa,MAAI,yBAAA,mCAAA,aAAc,aAAa,KAAI,iBAChE,IAAI;oBACF,MAAM,CAAC,aAAa,YAAY,GAAG,MAAM,QAAQ,GAAG,CAAC;wBACnD,aAAa,aAAa;wBAC1B,aAAa,aAAa;qBAC3B;oBAED,cAAc;oBACd,IAAI,eAAe,YAAY,EAAE,KAAK,QAAQ;wBAC5C,MAAM,gBAAgB;4BACpB,GAAG,YAAY;4BACf;4BACA;wBACF;wBAEA,+BAA+B;wBAC/B,WAAW;4BACT,YAAO,CAAC,IAAI,CAAC;wBACf,GAAG;oBACL,OAAO;wBACL,QAAQ,KAAK,CAAC;wBACd,aAAO,CAAC,KAAK,CAAC;oBAChB;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,uBAAuB;oBACrC,aAAO,CAAC,KAAK,CAAC;gBAChB;qBAEA,8BAA8B;gBAC9B,YAAO,CAAC,IAAI,CAAC;YAEjB,OAAO;gBACL,QAAQ,KAAK,CAAC;gBACd,aAAO,CAAC,KAAK,CAAC;YAChB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,aAAO,CAAC,KAAK,CAAC;QAChB,SAAU;YACR,mBAAmB;QACrB;IACF;IAEA,qBACE;;0BAEE,2BAAC;gBAAM,yBAAyB;oBAAE,QAAQ;gBAAO;;;;;;0BAEjD,2BAAC,UAAI;gBACH,WAAU;gBACV,OAAO;oBACL,cAAc;oBACd,WAAW;oBACX,QAAQ;oBACR,YAAY;gBACd;gBACA,qBACE,2BAAC,UAAI;oBAAC,SAAQ;oBAAgB,OAAM;8BAClC,cAAA,2BAAC;wBAAM,OAAO;wBAAG,OAAO;4BACtB,QAAQ;4BACR,YAAY;4BACZ,sBAAsB;4BACtB,qBAAqB;4BACrB,YAAY;wBACd;kCAAG;;;;;;;;;;;0BAMR,sBACC,2BAAC,WAAK;oBACJ,SAAQ;oBACR,aAAa;oBACb,MAAK;oBACL,QAAQ;oBACR,OAAO;wBAAE,cAAc;oBAAG;;;;;yCAG5B,2BAAC,UAAI;oBAAC,UAAU;8BACd,cAAA,2BAAC,UAAI;wBACH,YAAY;wBACZ,YAAY,CAAC;gCAyKQ,aAoBA,cAoBA,cAoBA;iDApOnB,2BAAC,UAAI,CAAC,IAAI;0CACR,cAAA,2BAAC,UAAI;oCACH,WAAU;oCACV,OAAO;wCACL,YAAY,CAAA,wBAAA,kCAAA,YAAa,EAAE,MAAK,KAAK,EAAE,GACnC,8CACA;wCACJ,cAAc;wCACd,WAAW,CAAA,wBAAA,kCAAA,YAAa,EAAE,MAAK,KAAK,EAAE,GAClC,uCACA;wCACJ,OAAO;wCACP,YAAY,CAAC,UAAU,EAAE,KAAK,SAAS,GAAG,YAAY,UAAU,CAAC;wCACjE,YAAY;wCACZ,QAAQ,CAAA,wBAAA,kCAAA,YAAa,EAAE,MAAK,KAAK,EAAE,GAC/B,sBACA;wCACJ,SAAS;wCACT,UAAU;wCACV,UAAU;oCACZ;oCACA,SAAS;oCACT,cAAc,CAAC;wCACb,IAAI,CAAA,wBAAA,kCAAA,YAAa,EAAE,MAAK,KAAK,EAAE,EAAE;4CAC/B,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;4CAClC,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;wCACpC;oCACF;oCACA,cAAc,CAAC;wCACb,IAAI,CAAA,wBAAA,kCAAA,YAAa,EAAE,MAAK,KAAK,EAAE,EAAE;4CAC/B,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;4CAClC,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;wCACpC;oCACF;8CAIA,cAAA,2BAAC,SAAG;wCAAC,QAAQ;4CAAC;4CAAG;yCAAE;wCAAE,OAAM;wCAAS,OAAO;4CAAE,OAAO;wCAAO;;0DAEzD,2BAAC,SAAG;gDAAC,IAAI;gDAAI,IAAI;gDAAI,IAAI;gDAAI,IAAI;gDAAI,IAAI;0DACvC,cAAA,2BAAC,UAAI;oDAAC,QAAQ;oDAAC,KAAK;;sEAElB,2BAAC,UAAI;4DAAC,OAAM;4DAAS,KAAK;4DAAG,MAAK;;8EAChC,2BAAC;oEACC,OAAO;wEACL,QAAQ;wEACR,SAAS;wEACT,cAAc;wEACd,YAAY;wEACZ,SAAS;wEACT,YAAY;wEACZ,KAAK;oEACP;oEACA,SAAS,IAAM,iBAAiB,KAAK,EAAE,EAAE,KAAK,IAAI;oEAClD,cAAc,CAAC;wEACb,EAAE,aAAa,CAAC,KAAK,CAAC,UAAU,GAAG;oEACrC;oEACA,cAAc,CAAC;wEACb,EAAE,aAAa,CAAC,KAAK,CAAC,UAAU,GAAG;oEACrC;;sFAEA,2BAAC;4EACC,MAAM;4EACN,OAAO;gFACL,UAAU;gFACV,OAAO,CAAA,wBAAA,kCAAA,YAAa,EAAE,MAAK,KAAK,EAAE,GAAG,YAAY;gFACjD,YAAY;4EACd;sFAEC,KAAK,IAAI;;;;;;sFAEZ,2BAAC,oBAAa;4EACZ,OAAO;gFACL,UAAU;gFACV,OAAO,CAAA,wBAAA,kCAAA,YAAa,EAAE,MAAK,KAAK,EAAE,GAAG,YAAY;gFACjD,eAAe;gFACf,SAAS;gFACT,YAAY;4EACd;;;;;;;;;;;;gEAKH,CAAA,wBAAA,kCAAA,YAAa,EAAE,MAAK,KAAK,EAAE,kBAC1B,2BAAC;oEAAK,OAAO;wEACX,YAAY;wEACZ,OAAO;wEACP,SAAS;wEACT,cAAc;wEACd,UAAU;wEACV,YAAY;oEACd;8EAAG;;;;;;gEAKJ,oBAAoB,KAAK,EAAE,kBAC1B,2BAAC,UAAI;oEAAC,OAAM;oEAAS,KAAK;;sFACxB,2BAAC,UAAI;4EAAC,MAAK;;;;;;sFACX,2BAAC;4EAAK,OAAO;gFAAE,UAAU;gFAAI,OAAO;4EAAO;sFAAG;;;;;;;;;;;;;;;;;;sEAMpD,2BAAC,UAAI;4DAAC,OAAM;4DAAS,KAAK;4DAAI,MAAK;;8EACjC,2BAAC,aAAO;oEAAC,OAAO,CAAC,MAAM,EAAE,IAAI,KAAK,KAAK,SAAS,EAAE,cAAc,CAAC,SAAS,CAAC;8EACzE,cAAA,2BAAC,UAAI;wEAAC,OAAM;wEAAS,KAAK;;0FACxB,2BAAC,0BAAmB;gFAAC,OAAO;oFAAE,OAAO;oFAAW,UAAU;gFAAG;;;;;;0FAC7D,2BAAC;gFAAK,OAAO;oFAAE,UAAU;oFAAI,OAAO;gFAAU;0FAC3C,IAAI,KAAK,KAAK,SAAS,EAAE,kBAAkB,CAAC;;;;;;;;;;;;;;;;;8EAKnD,2BAAC,aAAO;oEAAC,OAAO,CAAC,MAAM,EAAE,KAAK,WAAW,CAAC,CAAC,CAAC;8EAC1C,cAAA,2BAAC,UAAI;wEAAC,OAAM;wEAAS,KAAK;;0FACxB,2BAAC,mBAAY;gFAAC,OAAO;oFAAE,OAAO;oFAAW,UAAU;gFAAG;;;;;;0FACtD,2BAAC;gFAAK,OAAO;oFAAE,UAAU;oFAAI,OAAO;gFAAU;;oFAC3C,KAAK,WAAW;oFAAC;;;;;;;;;;;;;;;;;;8EAMxB,2BAAC;oEAAK,OAAO;wEACX,YAAY,KAAK,SAAS,GAAG,YAAY;wEACzC,OAAO;wEACP,SAAS;wEACT,cAAc;wEACd,UAAU;wEACV,YAAY;wEACZ,SAAS;wEACT,YAAY;wEACZ,KAAK;oEACP;8EACG,KAAK,SAAS,iBACb;;0FACE,2BAAC,oBAAa;gFAAC,OAAO;oFAAE,UAAU;gFAAE;;;;;;4EAAK;;qGAI3C;;0FACE,2BAAC,mBAAY;gFAAC,OAAO;oFAAE,UAAU;gFAAE;;;;;;4EAAK;;;;;;;;;;;;;;;;;;;;;;;;;0DAUpD,2BAAC,SAAG;gDAAC,IAAI;gDAAI,IAAI;gDAAI,IAAI;gDAAI,IAAI;gDAAI,IAAI;0DACvC,cAAA,2BAAC,SAAG;oDAAC,QAAQ;wDAAC;wDAAG;qDAAE;oDAAE,SAAS;wDAAE,IAAI;wDAAS,IAAI;oDAAM;;sEAErD,2BAAC,SAAG;4DAAC,IAAI;4DAAG,IAAI;4DAAG,IAAI;4DAAG,IAAI;4DAAG,IAAI;sEACnC,cAAA,2BAAC;gEAAI,OAAO;oEACV,YAAY;oEACZ,QAAQ;oEACR,cAAc;oEACd,SAAS;oEACT,WAAW;oEACX,UAAU;gEACZ;0EACE,cAAA,2BAAC,UAAI;oEAAC,QAAQ;oEAAC,OAAM;oEAAS,KAAK;;sFACjC,2BAAC,kBAAW;4EAAC,OAAO;gFAAE,OAAO;gFAAW,UAAU;4EAAG;;;;;;sFACrD,2BAAC;4EAAK,MAAM;4EAAC,OAAO;gFAAE,UAAU;gFAAI,OAAO;gFAAW,YAAY;4EAAE;sFACjE,EAAA,cAAA,KAAK,KAAK,cAAV,kCAAA,YAAY,QAAQ,KAAI;;;;;;sFAE3B,2BAAC;4EAAK,OAAO;gFAAE,UAAU;gFAAG,OAAO;4EAAO;sFAAG;;;;;;;;;;;;;;;;;;;;;;sEAMnD,2BAAC,SAAG;4DAAC,IAAI;4DAAG,IAAI;4DAAG,IAAI;4DAAG,IAAI;4DAAG,IAAI;sEACnC,cAAA,2BAAC;gEAAI,OAAO;oEACV,YAAY;oEACZ,QAAQ;oEACR,cAAc;oEACd,SAAS;oEACT,WAAW;oEACX,UAAU;gEACZ;0EACE,cAAA,2BAAC,UAAI;oEAAC,QAAQ;oEAAC,OAAM;oEAAS,KAAK;;sFACjC,2BAAC,mBAAY;4EAAC,OAAO;gFAAE,OAAO;gFAAW,UAAU;4EAAG;;;;;;sFACtD,2BAAC;4EAAK,MAAM;4EAAC,OAAO;gFAAE,UAAU;gFAAI,OAAO;gFAAW,YAAY;4EAAE;sFACjE,EAAA,eAAA,KAAK,KAAK,cAAV,mCAAA,aAAY,SAAS,KAAI;;;;;;sFAE5B,2BAAC;4EAAK,OAAO;gFAAE,UAAU;gFAAG,OAAO;4EAAO;sFAAG;;;;;;;;;;;;;;;;;;;;;;sEAMnD,2BAAC,SAAG;4DAAC,IAAI;4DAAG,IAAI;4DAAG,IAAI;4DAAG,IAAI;4DAAG,IAAI;sEACnC,cAAA,2BAAC;gEAAI,OAAO;oEACV,YAAY;oEACZ,QAAQ;oEACR,cAAc;oEACd,SAAS;oEACT,WAAW;oEACX,UAAU;gEACZ;0EACE,cAAA,2BAAC,UAAI;oEAAC,QAAQ;oEAAC,OAAM;oEAAS,KAAK;;sFACjC,2BAAC,gCAAyB;4EAAC,OAAO;gFAAE,OAAO;gFAAW,UAAU;4EAAG;;;;;;sFACnE,2BAAC;4EAAK,MAAM;4EAAC,OAAO;gFAAE,UAAU;gFAAI,OAAO;gFAAW,YAAY;4EAAE;sFACjE,EAAA,eAAA,KAAK,KAAK,cAAV,mCAAA,aAAY,QAAQ,KAAI;;;;;;sFAE3B,2BAAC;4EAAK,OAAO;gFAAE,UAAU;gFAAG,OAAO;4EAAO;sFAAG;;;;;;;;;;;;;;;;;;;;;;sEAMnD,2BAAC,SAAG;4DAAC,IAAI;4DAAG,IAAI;4DAAG,IAAI;4DAAG,IAAI;4DAAG,IAAI;sEACnC,cAAA,2BAAC;gEAAI,OAAO;oEACV,YAAY;oEACZ,QAAQ;oEACR,cAAc;oEACd,SAAS;oEACT,WAAW;oEACX,UAAU;gEACZ;0EACE,cAAA,2BAAC,UAAI;oEAAC,QAAQ;oEAAC,OAAM;oEAAS,KAAK;;sFACjC,2BAAC,gCAAyB;4EAAC,OAAO;gFAAE,OAAO;gFAAW,UAAU;4EAAG;;;;;;sFACnE,2BAAC;4EAAK,MAAM;4EAAC,OAAO;gFAAE,UAAU;gFAAI,OAAO;gFAAW,YAAY;4EAAE;sFACjE,EAAA,eAAA,KAAK,KAAK,cAAV,mCAAA,aAAY,OAAO,KAAI;;;;;;sFAE1B,2BAAC;4EAAK,OAAO;gFAAE,UAAU;gFAAG,OAAO;4EAAO;sFAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgB3E;GA1XM;;QAOsC,aAAQ;;;KAP9C;IA4XN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BC8Lf;;;eAAA;;;;;;wEAroB2C;6BAqBpC;6BACqB;8BAUrB;;;;;;;;;;AAEP,MAAM,EAAE,IAAI,EAAE,GAAG,gBAAU;AAC3B,MAAM,EAAE,OAAO,EAAE,GAAG,UAAI;AASxB,MAAM,iBAAgD,CAAC;;IACrD,aAAa;IACb,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,eAAQ,EAAiB,EAAE;IACrE,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,eAAQ,EAAoB;QAC5D,mBAAmB;QACnB,qBAAqB;QACrB,kBAAkB;QAClB,YAAY;QACZ,gBAAgB;QAChB,sBAAsB;IACxB;IACA,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAC;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,eAAQ,EAAgB;IAElD,WAAW;IACX,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,IAAA,eAAQ,EAAC;IACzD,MAAM,CAAC,SAAS,GAAG,UAAI,CAAC,OAAO;IAC/B,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,eAAQ,EAAgB;IAElE,QAAQ;IACR,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,eAAQ,EACxC;IAEF,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,eAAQ,EAAC;IAE7C,WAAW;IACX,IAAA,gBAAS,EAAC;QACR,MAAM,gBAAgB;YACpB,IAAI;gBACF,WAAW;gBACX,SAAS;gBAET,QAAQ,GAAG,CAAC;gBAEZ,8BAA8B;gBAC9B,MAAM,eAAe,iBAAW,CAAC,YAAY,GAAG,KAAK,CAAC,CAAA;oBACpD,QAAQ,KAAK,CAAC,eAAe;oBAC7B,OAAO,EAAE;gBACX;gBAEA,MAAM,eAAe,iBAAW,CAAC,YAAY,GAAG,KAAK,CAAC,CAAA;oBACpD,QAAQ,KAAK,CAAC,eAAe;oBAC7B,OAAO;wBACL,mBAAmB;wBACnB,qBAAqB;wBACrB,kBAAkB;wBAClB,YAAY;wBACZ,gBAAgB;wBAChB,sBAAsB;oBACxB;gBACF;gBAEA,MAAM,CAAC,OAAO,MAAM,GAAG,MAAM,QAAQ,GAAG,CAAC;oBAAC;oBAAc;iBAAa;gBAErE,QAAQ,GAAG,CAAC,8BAA8B;gBAC1C,QAAQ,GAAG,CAAC,4BAA4B;gBAExC,iBAAiB;gBACjB,aAAa;YACf,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,oBAAoB;gBAClC,SAAS;YACX,SAAU;gBACR,WAAW;YACb;QACF;QAEA;IACF,GAAG,EAAE;IAEL,mBAAmB;IACnB,MAAM,wBAAwB,cAAc,MAAM,CAAC,CAAC;QAClD,SAAS;QACT,IAAI,cAAc,aAAa,KAAK,MAAM,KAAK,GAAG,OAAO;QACzD,IAAI,cAAc,eAAe,KAAK,MAAM,KAAK,GAAG,OAAO;QAE3D,WAAW;QACX,IACE,cACA,CAAC,KAAK,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,KAEzD,OAAO;QAGT,OAAO;IACT;IAEA,WAAW;IACX,MAAM,yBAAyB,OAAO;QACpC,IAAI;YACF,MAAM,OAAO,cAAc,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;YAC9C,IAAI,CAAC,MAAM;gBACT,aAAO,CAAC,KAAK,CAAC;gBACd;YACF;YAEA,MAAM,YAAY,KAAK,MAAM,KAAK,IAAI,IAAI;YAC1C,QAAQ,GAAG,CAAC,CAAC,uBAAuB,EAAE,GAAG,IAAI,EAAE,UAAU,CAAC;YAE1D,MAAM,iBAAW,CAAC,UAAU,CAAC,IAAI;gBAAE,QAAQ;YAAU;YAErD,SAAS;YACT,iBACE,cAAc,GAAG,CAAC,CAAC,OACjB,KAAK,EAAE,KAAK,KAAK;oBAAE,GAAG,IAAI;oBAAE,QAAQ;gBAAU,IAAI;YAItD,SAAS;YACT,IAAI;gBACF,MAAM,QAAQ,MAAM,iBAAW,CAAC,YAAY;gBAC5C,aAAa;YACf,EAAE,OAAO,YAAY;gBACnB,QAAQ,KAAK,CAAC,aAAa;YAC3B,kBAAkB;YACpB;YAEA,aAAO,CAAC,OAAO,CAAC,cAAc,IAAI,UAAU;QAC9C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,aAAO,CAAC,KAAK,CAAC;QAChB;IACF;IAEA,MAAM,wBAAwB,OAAO;QACnC,IAAI;YACF,QAAQ,GAAG,CAAC,wBAAwB;gBAAE;gBAAe;YAAO;YAE5D,IAAI,eAAe;gBACjB,WAAW;gBACX,MAAM,cAAc,MAAM,iBAAW,CAAC,UAAU,CAAC,eAAe;oBAC9D,OAAO,OAAO,IAAI;oBAClB,UAAU,OAAO,QAAQ;gBAC3B;gBAEA,iBACE,cAAc,GAAG,CAAC,CAAC,OACjB,KAAK,EAAE,KAAK,gBAAgB,cAAc;gBAG9C,aAAO,CAAC,OAAO,CAAC;YAClB,OAAO;gBACL,UAAU;gBACV,MAAM,UAAU,MAAM,iBAAW,CAAC,UAAU,CAAC;oBAC3C,OAAO,OAAO,IAAI;oBAClB,UAAU,OAAO,QAAQ;gBAC3B;gBAEA,iBAAiB;oBAAC;uBAAY;iBAAc;gBAC5C,aAAO,CAAC,OAAO,CAAC;YAClB;YAEA,SAAS;YACT,IAAI;gBACF,MAAM,QAAQ,MAAM,iBAAW,CAAC,YAAY;gBAC5C,aAAa;YACf,EAAE,OAAO,YAAY;gBACnB,QAAQ,KAAK,CAAC,aAAa;YAC3B,kBAAkB;YACpB;YAEA,aAAa;YACb,oBAAoB;YACpB,iBAAiB;YACjB,SAAS,WAAW;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,MAAM,SAAS,gBAAgB,OAAO;YACtC,aAAO,CAAC,KAAK,CAAC,CAAC,EAAE,OAAO,eAAe,CAAC;QAC1C;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,QAAQ,GAAG,CAAC,wBAAwB;YAEpC,MAAM,iBAAW,CAAC,UAAU,CAAC;YAC7B,iBAAiB,cAAc,MAAM,CAAC,CAAC,OAAS,KAAK,EAAE,KAAK;YAE5D,SAAS;YACT,IAAI;gBACF,MAAM,QAAQ,MAAM,iBAAW,CAAC,YAAY;gBAC5C,aAAa;YACf,EAAE,OAAO,YAAY;gBACnB,QAAQ,KAAK,CAAC,aAAa;YAC3B,kBAAkB;YACpB;YAEA,aAAO,CAAC,OAAO,CAAC;QAClB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,aAAO,CAAC,KAAK,CAAC;QAChB;IACF;IAEA,qBACE,2BAAC,UAAI;QACH,WAAU;QACV,OAAO;YACL,cAAc;YACd,WAAW;YACX,QAAQ;YACR,YAAY;QACd;QACA,qBACE,2BAAC,UAAI;YAAC,SAAQ;YAAgB,OAAM;sBAClC,cAAA,2BAAC;gBAAK,MAAM;0BAAC;;;;;;;;;;;;0BAKjB,2BAAC;gBACC,OAAO;oBACL,cAAc;oBACd,SAAS;oBACT,YAAY;oBACZ,cAAc;oBACd,QAAQ;gBACV;0BAGA,cAAA,2BAAC,SAAG;oBAAC,QAAQ;wBAAC;wBAAI;qBAAG;oBAAE,OAAM;;sCAE3B,2BAAC,SAAG;4BAAC,IAAI;4BAAI,IAAI;4BAAI,IAAI;4BAAG,IAAI;4BAAG,IAAI;sCACrC,cAAA,2BAAC,UAAI;gCAAC,OAAM;gCAAS,KAAK;gCAAI,OAAO;oCAAE,OAAO;gCAAO;;kDACnD,2BAAC,WAAK,CAAC,MAAM;wCACX,aAAY;wCACZ,UAAU;wCACV,sBAAQ,2BAAC,qBAAc;;;;;wCACvB,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wCAC7C,OAAO;4CAAE,MAAM;wCAAE;wCACjB,MAAK;;;;;;kDAGP,2BAAC,YAAM;wCACL,MAAK;wCACL,oBAAM,2BAAC,mBAAY;;;;;wCACnB,SAAS;4CACP,iBAAiB;4CACjB,SAAS,WAAW;4CACpB,oBAAoB;wCACtB;wCACA,OAAO;4CACL,YAAY;4CACZ,aAAa;4CACb,WAAW;4CACX,YAAY;4CACZ,UAAU;wCACZ;wCACA,MAAK;kDACN;;;;;;;;;;;;;;;;;sCAOL,2BAAC,SAAG;4BAAC,IAAI;4BAAI,IAAI;4BAAI,IAAI;4BAAG,IAAI;4BAAG,IAAI;sCACrC,cAAA,2BAAC,UAAI;gCACH,OAAM;gCACN,SAAQ;gCACR,MAAK;0CAEL,cAAA,2BAAC,WAAK;oCAAC,MAAM;oCAAI,IAAI;;sDACnB,2BAAC,aAAO;4CAAC,OAAO,CAAC,QAAQ,EAAE,UAAU,iBAAiB,CAAC,CAAC,CAAC;sDACvD,cAAA,2BAAC,UAAI;gDAAC,OAAM;gDAAS,KAAK;;kEACxB,2BAAC;wDACC,OAAO;4DACL,OAAO;4DACP,QAAQ;4DACR,cAAc;4DACd,YAAY;wDACd;;;;;;kEAEF,2BAAC;wDAAK,OAAO;4DAAE,UAAU;4DAAI,YAAY;4DAAK,OAAO;wDAAU;;4DAAG;4DAC5D,UAAU,iBAAiB;;;;;;;;;;;;;;;;;;sDAKrC,2BAAC,aAAO;4CAAC,OAAO,CAAC,QAAQ,EAAE,UAAU,mBAAmB,CAAC,CAAC,CAAC;sDACzD,cAAA,2BAAC,UAAI;gDAAC,OAAM;gDAAS,KAAK;;kEACxB,2BAAC;wDACC,OAAO;4DACL,OAAO;4DACP,QAAQ;4DACR,cAAc;4DACd,YAAY;wDACd;;;;;;kEAEF,2BAAC;wDAAK,OAAO;4DAAE,UAAU;4DAAI,YAAY;4DAAK,OAAO;wDAAU;;4DAAG;4DAC5D,UAAU,mBAAmB;;;;;;;;;;;;;;;;;;sDAKvC,2BAAC,aAAO;4CAAC,OAAO,CAAC,QAAQ,EAAE,UAAU,gBAAgB,CAAC,CAAC,CAAC;sDACtD,cAAA,2BAAC,UAAI;gDAAC,OAAM;gDAAS,KAAK;;kEACxB,2BAAC;wDACC,OAAO;4DACL,OAAO;4DACP,QAAQ;4DACR,cAAc;4DACd,YAAY;wDACd;;;;;;kEAEF,2BAAC;wDAAK,OAAO;4DAAE,UAAU;4DAAI,YAAY;4DAAK,OAAO;wDAAU;;4DAAG;4DAC5D,UAAU,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAS1C,2BAAC,SAAG;4BAAC,IAAI;4BAAI,IAAI;4BAAI,IAAI;4BAAG,IAAI;4BAAG,IAAI;sCACrC,cAAA,2BAAC,UAAI;gCACH,OAAM;gCACN,SAAQ;0CAER,cAAA,2BAAC,aAAO;oCAAC,OAAO,CAAC,KAAK,EAAE,UAAU,oBAAoB,CAAC,GAAG,EAAE,UAAU,cAAc,CAAC,CAAC,EAAE,UAAU,UAAU,CAAC,CAAC,CAAC;8CAC7G,cAAA,2BAAC,UAAI;wCAAC,OAAM;wCAAS,KAAK;;0DACxB,2BAAC;gDAAK,OAAO;oDAAE,UAAU;oDAAI,YAAY;oDAAK,OAAO;gDAAU;0DAAG;;;;;;0DAGlE,2BAAC,cAAQ;gDACP,SAAS,UAAU,oBAAoB;gDACvC,MAAK;gDACL,OAAO;oDAAE,OAAO;gDAAG;gDACnB,aAAY;gDACZ,UAAU;;;;;;0DAEZ,2BAAC;gDAAK,OAAO;oDAAE,UAAU;oDAAI,YAAY;oDAAK,OAAO;gDAAU;;oDAC5D,UAAU,oBAAoB;oDAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAU9C,2BAAC,UAAI;gBACH,WAAW;gBACX,UAAU,CAAC,MACT,aAAa;gBAEf,MAAK;gBACL,OAAO;oBAAE,cAAc;gBAAE;;kCAEzB,2BAAC;wBAAQ,KAAI;uBAAS;;;;;kCACtB,2BAAC;wBAAQ,KAAI;uBAAU;;;;;kCACvB,2BAAC;wBAAQ,KAAI;uBAAU;;;;;;;;;;;YAIxB,sBACC,2BAAC,WAAK;gBACJ,SAAQ;gBACR,aAAa;gBACb,MAAK;gBACL,QAAQ;gBACR,OAAO;oBAAE,cAAc;gBAAG;;;;;qCAG5B,2BAAC,UAAI;gBAAC,UAAU;;kCACd,2BAAC,UAAI;wBACH,YAAY;wBACZ,YAAY,CAAC;4BACf,qBACE,2BAAC,UAAI,CAAC,IAAI;gCACR,WAAU;gCACV,OAAO;oCACL,SAAS;oCACT,cAAc;oCACd,cAAc;oCACd,YAAY;oCACZ,SAAS,KAAK,MAAM,KAAK,IAAI,MAAM;oCACnC,YAAY,CAAC,UAAU,EACrB,KAAK,MAAM,KAAK,IACZ,YACA,KAAK,QAAQ,KAAK,IAClB,YACA,KAAK,QAAQ,KAAK,IAClB,YACA,UACL,CAAC;oCACF,WAAW;gCACb;0CAEA,cAAA,2BAAC,UAAI;oCAAC,OAAM;oCAAS,KAAK;oCAAI,OAAO;wCAAE,OAAO;oCAAO;;sDAEnD,2BAAC,UAAI;4CAAC,QAAQ;4CAAC,OAAM;;gDAClB,KAAK,MAAM,KAAK,kBACf,2BAAC,UAAI;oDACH,OAAM;oDACN,SAAQ;oDACR,OAAO;wDACL,OAAO;wDACP,QAAQ;wDACR,cAAc;wDACd,YAAY;oDACd;8DAEA,cAAA,2BAAC,oBAAa;wDACZ,OAAO;4DAAE,OAAO;4DAAQ,UAAU;wDAAG;;;;;;;;;;2EAIzC,2BAAC;oDACC,OAAO;wDACL,OAAO;wDACP,QAAQ;wDACR,cAAc;wDACd,QAAQ,CAAC,UAAU,EACjB,KAAK,QAAQ,KAAK,IACd,YACA,KAAK,QAAQ,KAAK,IAClB,YACA,UACL,CAAC;oDACJ;;;;;;8DAIJ,2BAAC;oDACC,OAAO;wDACL,OAAO;wDACP,QAAQ;wDACR,YAAY;wDACZ,WAAW;oDACb;;;;;;;;;;;;sDAKJ,2BAAC,UAAI;4CAAC,QAAQ;4CAAC,OAAO;gDAAE,MAAM;4CAAE;;8DAC9B,2BAAC;oDACC,OAAO;wDACL,UAAU;wDACV,YACE,KAAK,QAAQ,KAAK,IAAI,MAAM;wDAC9B,gBAAgB,KAAK,MAAM,KAAK,IAC5B,iBACA;wDACJ,OAAO,KAAK,MAAM,KAAK,IAAI,YAAY;oDACzC;8DAEC,KAAK,KAAK;;;;;;8DAIb,2BAAC,WAAK;oDAAC,OAAM;oDAAS,MAAM;oDAAG,OAAO;wDAAE,WAAW;oDAAE;;sEACnD,2BAAC,uBAAgB;4DACf,OAAO;gEACL,UAAU;gEACV,OAAO;4DACT;;;;;;sEAEF,2BAAC;4DAAK,MAAK;4DAAY,OAAO;gEAAE,UAAU;4DAAG;;gEAAG;gEACxC,IAAI,KAAK,KAAK,SAAS,EAAE,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;;sDAMxD,2BAAC,cAAQ;4CACP,SAAS;gDAAC;6CAAQ;4CAClB,MAAM;gDACJ,OAAO;oDACL;wDACE,KAAK;wDACL,OAAO,KAAK,MAAM,KAAK,IAAI,UAAU;wDACrC,oBACE,2BAAC,oBAAa;4DACZ,OAAO;gEACL,OAAO,KAAK,MAAM,KAAK,IAAI,YAAY;gEACvC,UAAU;4DACZ;;;;;;oDAGN;oDACA;wDACE,KAAK;wDACL,OAAO;wDACP,oBAAM,2BAAC,mBAAY;4DAAC,OAAO;gEAAE,OAAO;4DAAU;;;;;;oDAChD;oDACA;wDACE,KAAK;wDACL,OAAO;wDACP,oBAAM,2BAAC,qBAAc;4DAAC,OAAO;gEAAE,OAAO;4DAAU;;;;;;wDAChD,QAAQ;oDACV;iDACD;gDACD,SAAS,CAAC,EAAE,GAAG,EAAE;oDACf,IAAI,QAAQ,YACV,uBAAuB,KAAK,EAAE;yDACzB,IAAI,QAAQ,QAAQ;wDACzB,iBAAiB,KAAK,EAAE;wDACxB,SAAS,cAAc,CAAC;4DACtB,MAAM,KAAK,KAAK;4DAChB,UAAU,KAAK,QAAQ;wDACzB;wDACA,oBAAoB;oDACtB,OAAO,IAAI,QAAQ,UACjB,iBAAiB,KAAK,EAAE;gDAE5B;4CACF;sDAEA,cAAA,2BAAC,YAAM;gDACL,MAAK;gDACL,MAAK;gDACL,oBAAM,2BAAC,mBAAY;;;;;gDACnB,OAAO;oDAAE,OAAO;oDAAI,QAAQ;gDAAG;;;;;;;;;;;;;;;;;;;;;;wBAM3C;;;;;;kCAIF,2BAAC,WAAK;wBACJ,OAAO,gBAAgB,WAAW;wBAClC,MAAM;wBACN,UAAU;4BACR,oBAAoB;4BACpB,SAAS,WAAW;wBACtB;wBACA,MAAM;4BACJ,SAAS,MAAM;wBACjB;wBACA,QAAQ;wBACR,cAAc;wBACd,QAAQ;0CACN,2BAAC,YAAM;gCAAc,SAAS,IAAM,oBAAoB;0CAAQ;+BAApD;;;;;0CAGZ,2BAAC,YAAM;gCAEL,MAAK;gCACL,SAAS;oCACP,SAAS,MAAM;gCACjB;gCACA,OAAO;oCACL,YAAY;oCACZ,aAAa;oCACb,WAAW;gCACb;0CAEC,gBAAgB,SAAS;+BAXtB;;;;;yBAaP;kCAED,cAAA,2BAAC,UAAI;4BACH,MAAM;4BACN,QAAO;4BACP,UAAU;4BACV,cAAa;;8CAEb,2BAAC,UAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,OAAO;wCAAC;4CAAE,UAAU;4CAAM,SAAS;wCAAU;qCAAE;8CAE/C,cAAA,2BAAC,WAAK;wCACJ,aAAY;wCACZ,MAAK;wCACL,OAAO;4CAAE,cAAc;wCAAE;;;;;;;;;;;8CAI7B,2BAAC,UAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,cAAc;oCACd,OAAO;wCAAC;4CAAE,UAAU;4CAAM,SAAS;wCAAS;qCAAE;8CAE9C,cAAA,2BAAC,YAAM;wCACL,MAAK;wCACL,SAAS;4CACP;gDAAE,OAAO;gDAAG,OAAO;4CAAO;4CAC1B;gDAAE,OAAO;gDAAG,OAAO;4CAAO;4CAC1B;gDAAE,OAAO;gDAAG,OAAO;4CAAO;yCAC3B;wCACD,OAAO;4CAAE,cAAc;wCAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASvC;GAvlBM;;QAgBe,UAAI,CAAC;;;KAhBpB;IAylBN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BC/Pf;;;eAAA;;;;;;8BAjYO;6BAWA;wEACoC;6BACf;;;;;;;;;;AAG5B,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,gBAAU;AAElC,MAAM,kBAA4B;;IAChC,WAAW;IACX,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,eAAQ,EAA4B;QAClE,MAAM;QACN,UAAU;QACV,OAAO;QACP,WAAW;QACX,cAAc;QACd,eAAe;QACf,eAAe;QACf,WAAW;QACX,QAAQ;IACV;IACA,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,IAAA,eAAQ,EAAC;IACvD,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,eAAQ,EAAgB;IAElE,WAAW;IACX,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,eAAQ,EAA4B;QAC5E,UAAU;QACV,WAAW;QACX,UAAU;QACV,QAAQ;IACV;IACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,eAAQ,EAAC;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,eAAQ,EAAgB;IAI5D,OAAO;IAEP,SAAS;IACT,IAAA,gBAAS,EAAC;QACR,QAAQ,GAAG,CAAC;QAEZ,MAAM,gBAAgB;YACpB,IAAI;gBACF,QAAQ,GAAG,CAAC;gBAEZ,8BAA8B;gBAC9B,MAAM,oBAAoB,iBAAW,CAAC,oBAAoB,GAAG,KAAK,CAAC,CAAA;oBACjE,QAAQ,KAAK,CAAC,eAAe;oBAC7B,iBAAiB;oBACjB,OAAO;gBACT;gBAEA,MAAM,eAAe,iBAAW,CAAC,oBAAoB,GAAG,KAAK,CAAC,CAAA;oBAC5D,QAAQ,KAAK,CAAC,aAAa;oBAC3B,cAAc;oBACd,OAAO;gBACT;gBAEA,MAAM,CAAC,YAAY,MAAM,GAAG,MAAM,QAAQ,GAAG,CAAC;oBAAC;oBAAmB;iBAAa;gBAE/E,IAAI,YAAY;oBACd,QAAQ,GAAG,CAAC,+BAA+B;oBAC3C,YAAY;oBACZ,iBAAiB;gBACnB;gBAEA,IAAI,OAAO;oBACT,QAAQ,GAAG,CAAC,6BAA6B;oBACzC,iBAAiB;oBACjB,cAAc;gBAChB;YAEF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,kBAAkB;gBAChC,iBAAiB;gBACjB,cAAc;YAChB,SAAU;gBACR,mBAAmB;gBACnB,gBAAgB;YAClB;QACF;QAEA;IACF,GAAG,EAAE;IAIL,qBACE;kBAEG,8BACC,2BAAC,WAAK;YACJ,SAAQ;YACR,aAAa;YACb,MAAK;YACL,QAAQ;YACR,OAAO;gBAAE,cAAc;YAAG;;;;;iCAG5B,2BAAC,UAAI;YAAC,UAAU;sBAEd,cAAA,2BAAC,UAAI;gBACH,OAAO;oBACL,YAAY;oBACZ,cAAc;oBACd,OAAO;oBACP,UAAU;oBACV,UAAU;oBACV,WAAW;oBACX,QAAQ;gBACV;gBACA,QAAQ;oBACN,MAAM;wBACJ,SAAS;wBACT,QAAQ;oBACV;gBACF;;kCAIE,2BAAC;wBACC,OAAO;4BACL,UAAU;4BACV,KAAK;4BACL,OAAO;4BACP,OAAO;4BACP,QAAQ;4BACR,YAAY;4BACZ,cAAc;wBAChB;;;;;;kCAEF,2BAAC;wBACC,OAAO;4BACL,UAAU;4BACV,QAAQ;4BACR,MAAM;4BACN,OAAO;4BACP,QAAQ;4BACR,YAAY;4BACZ,cAAc;wBAChB;;;;;;kCAEF,2BAAC;wBACC,OAAO;4BACL,UAAU;4BACV,KAAK;4BACL,OAAO;4BACP,OAAO;4BACP,QAAQ;4BACR,YAAY;4BACZ,cAAc;4BACd,WAAW;wBACb;;;;;;kCAIF,2BAAC,SAAG;wBACF,QAAQ;4BAAC;4BAAI;yBAAG;wBAChB,OAAM;wBACN,OAAO;4BAAE,UAAU;4BAAY,QAAQ;4BAAG,OAAO;4BAAQ,WAAW;wBAAO;;0CAG3E,2BAAC,SAAG;gCAAC,IAAI;gCAAI,IAAI;gCAAI,IAAI;gCAAG,IAAI;gCAAG,IAAI;0CACrC,cAAA,2BAAC,UAAI;oCAAC,OAAM;oCAAS,OAAO;wCAAE,WAAW;oCAAO;;sDAE9C,2BAAC,YAAM;4CACL,MAAM;4CACN,OAAM;4CACN,OAAO;gDACL,iBAAiB;gDACjB,aAAa;gDACb,UAAU;gDACV,YAAY;gDACZ,QAAQ;4CACV;sDAEC,SAAS,IAAI,GAAG,SAAS,IAAI,CAAC,MAAM,CAAC,GAAG,WAAW,mBAAK,2BAAC,mBAAY;;;;;;;;;;sDAIxE,2BAAC,WAAK;4CAAC,WAAU;4CAAW,MAAM;;8DAChC,2BAAC;oDACC,OAAO;oDACP,OAAO;wDACL,QAAQ;wDACR,OAAO;wDACP,UAAU;wDACV,YAAY;oDACd;8DAEC,SAAS,IAAI,IAAI;;;;;;8DAIpB,2BAAC,WAAK;oDAAC,WAAU;oDAAW,MAAM;;wDAC/B,SAAS,KAAK,kBACb,2BAAC,WAAK;4DAAC,MAAM;4DAAG,OAAM;;8EACpB,2BAAC,mBAAY;oEAAC,OAAO;wEAAE,UAAU;wEAAI,OAAO;oEAAwB;;;;;;8EACpE,2BAAC;oEAAK,OAAO;wEAAE,OAAO;wEAAyB,UAAU;oEAAG;8EACzD,SAAS,KAAK;;;;;;;;;;;;wDAIpB,SAAS,SAAS,kBACjB,2BAAC,WAAK;4DAAC,MAAM;4DAAG,OAAM;;8EACpB,2BAAC,oBAAa;oEAAC,OAAO;wEAAE,UAAU;wEAAI,OAAO;oEAAwB;;;;;;8EACrE,2BAAC;oEAAK,OAAO;wEAAE,OAAO;wEAAyB,UAAU;oEAAG;8EACzD,SAAS,SAAS;;;;;;;;;;;;;;;;;;gDAO1B,SAAS,YAAY,kBACpB,2BAAC;oDAAK,OAAO;wDAAE,UAAU;wDAAI,OAAO;wDAAyB,YAAY;oDAAI;;wDAAG;wDACzE,SAAS,YAAY;;;;;;;;;;;;;;;;;;;;;;;;0CAQpC,2BAAC,SAAG;gCAAC,IAAI;gCAAI,IAAI;gCAAI,IAAI;gCAAG,IAAI;gCAAI,IAAI;0CACtC,cAAA,2BAAC,UAAI;oCACH,QAAQ;oCACR,SAAQ;oCACR,OAAO;wCAAE,WAAW;wCAAQ,WAAW;wCAAU,SAAS;oCAAQ;;sDAGlE,2BAAC,WAAK;4CACJ,OAAM;4CACN,OAAO;gDACL,gBAAgB;gDAChB,cAAc;4CAChB;;8DAEA,2BAAC,uBAAgB;oDACf,OAAO;wDACL,UAAU;wDACV,OAAO;oDACT;;;;;;8DAEF,2BAAC;oDAAK,OAAO;wDAAE,OAAO;wDAAyB,UAAU;wDAAI,YAAY;oDAAI;8DAAG;;;;;;;;;;;;wCAMjF,2BACC,2BAAC;4CAAK,OAAO;gDAAE,UAAU;gDAAI,OAAO;4CAAwB;sDAAG;;;;;iEAI/D,2BAAC,UAAI;4CAAC,UAAU;sDACd,cAAA,2BAAC,SAAG;gDAAC,QAAQ;oDAAC;oDAAG;iDAAE;gDAAE,SAAQ;;kEAC3B,2BAAC,SAAG;wDAAC,IAAI;wDAAG,IAAI;wDAAG,IAAI;wDAAG,IAAI;wDAAG,IAAI;kEACnC,cAAA,2BAAC;4DAAI,OAAO;gEAAE,WAAW;4DAAS;;8EAChC,2BAAC;oEAAI,OAAO;wEACV,UAAU;wEACV,YAAY;wEACZ,OAAO;wEACP,YAAY;oEACd;8EACG,cAAc,QAAQ;;;;;;8EAEzB,2BAAC;oEAAI,OAAO;wEACV,UAAU;wEACV,OAAO;wEACP,WAAW;oEACb;8EAAG;;;;;;;;;;;;;;;;;kEAKP,2BAAC,SAAG;wDAAC,IAAI;wDAAG,IAAI;wDAAG,IAAI;wDAAG,IAAI;wDAAG,IAAI;kEACnC,cAAA,2BAAC;4DAAI,OAAO;gEAAE,WAAW;4DAAS;;8EAChC,2BAAC;oEAAI,OAAO;wEACV,UAAU;wEACV,YAAY;wEACZ,OAAO;wEACP,YAAY;oEACd;8EACG,cAAc,SAAS;;;;;;8EAE1B,2BAAC;oEAAI,OAAO;wEACV,UAAU;wEACV,OAAO;wEACP,WAAW;oEACb;8EAAG;;;;;;;;;;;;;;;;;kEAKP,2BAAC,SAAG;wDAAC,IAAI;wDAAG,IAAI;wDAAG,IAAI;wDAAG,IAAI;wDAAG,IAAI;kEACnC,cAAA,2BAAC;4DAAI,OAAO;gEAAE,WAAW;4DAAS;;8EAChC,2BAAC;oEAAI,OAAO;wEACV,UAAU;wEACV,YAAY;wEACZ,OAAO;wEACP,YAAY;oEACd;8EACG,cAAc,QAAQ;;;;;;8EAEzB,2BAAC;oEAAI,OAAO;wEACV,UAAU;wEACV,OAAO;wEACP,WAAW;oEACb;8EAAG;;;;;;;;;;;;;;;;;kEAKP,2BAAC,SAAG;wDAAC,IAAI;wDAAG,IAAI;wDAAG,IAAI;wDAAG,IAAI;wDAAG,IAAI;kEACnC,cAAA,2BAAC;4DAAI,OAAO;gEAAE,WAAW;4DAAS;;8EAChC,2BAAC;oEAAI,OAAO;wEACV,UAAU;wEACV,YAAY;wEACZ,OAAO;wEACP,YAAY;oEACd;8EACG,cAAc,MAAM;;;;;;8EAEvB,2BAAC;oEAAI,OAAO;wEACV,UAAU;wEACV,OAAO;wEACP,WAAW;oEACb;8EAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAYjB,2BAAC,SAAG;gCAAC,IAAI;gCAAI,IAAI;gCAAI,IAAI;gCAAG,IAAI;gCAAG,IAAI;0CACrC,cAAA,2BAAC,UAAI;oCACH,QAAQ;oCACR,SAAQ;oCACR,OAAO;wCAAE,WAAW;wCAAQ,SAAS;oCAAQ;8CAE7C,cAAA,2BAAC,WAAK;wCAAC,WAAU;wCAAW,MAAM;;0DAChC,2BAAC,WAAK;gDAAC,WAAU;gDAAW,MAAM;;kEAChC,2BAAC;wDAAK,OAAO;4DAAE,UAAU;4DAAI,OAAO;4DAAyB,YAAY;wDAAI;kEAAG;;;;;;kEAGhF,2BAAC;wDAAK,OAAO;4DAAE,UAAU;4DAAI,OAAO;4DAAS,YAAY;4DAAK,YAAY;wDAAI;kEAC3E,SAAS,aAAa,IAAI;;;;;;;;;;;;0DAG/B,2BAAC,WAAK;gDAAC,WAAU;gDAAW,MAAM;;kEAChC,2BAAC;wDAAK,OAAO;4DAAE,UAAU;4DAAI,OAAO;4DAAyB,YAAY;wDAAI;kEAAG;;;;;;kEAGhF,2BAAC;wDAAK,OAAO;4DAAE,UAAU;4DAAI,OAAO;4DAAS,YAAY;4DAAK,YAAY;wDAAI;kEAC3E,SAAS,aAAa,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYrD;GA7WM;KAAA;IA+WN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BC7Rf;;;eAAA;;;;;;;uEAzGkB;6BACmB;4BACZ;gFAEE;8EACF;iFACG;6EACA;;;;;;;;;;AAE5B,MAAM,qBAA+B;;IACnC,MAAM,EAAE,YAAY,EAAE,OAAO,EAAE,GAAG,IAAA,aAAQ,EAAC;IAE3C,gBAAgB;IAChB,IAAI,SACF,qBACE,2BAAC;QAAI,OAAO;YACV,WAAW;YACX,YAAY;YACZ,SAAS;YACT,gBAAgB;YAChB,YAAY;QACd;;0BACE,2BAAC,UAAI;gBAAC,MAAK;;;;;;0BACX,2BAAC;gBAAI,OAAO;oBAAE,YAAY;gBAAG;0BAAG;;;;;;;;;;;;IAKtC,iBAAiB;IACjB,IAAI,EAAC,yBAAA,mCAAA,aAAc,WAAW,GAC5B,qBACE,2BAAC;QAAI,OAAO;YACV,WAAW;YACX,YAAY;YACZ,SAAS;YACT,gBAAgB;YAChB,YAAY;QACd;kBACE,cAAA,2BAAC;sBAAI;;;;;;;;;;;IAKX,qBACE,2BAAC;QAAI,OAAO;YACV,WAAW;YACX,YAAY;YACZ,SAAS,sBAAsB,YAAY;QAC7C;;0BAEE,2BAAC,UAAI;gBACH,OAAO;oBACL,OAAO;oBACP,WAAW;oBACX,cAAc;oBACd,WAAW;gBACb;gBACA,QAAQ;oBACN,MAAM;wBACJ,SAAS;wBACT,6BAA6B;4BAC3B,SAAS;wBACX;oBACF;gBACF;0BAEA,cAAA,2BAAC,SAAG;oBAAC,QAAQ;wBAAC;wBAAI;qBAAG;oBAAE,OAAO;wBAAE,QAAQ;oBAAE;;sCAExC,2BAAC,SAAG;4BAAC,IAAI;4BAAI,OAAO;gCAAE,cAAc;4BAAE;sCACpC,cAAA,2BAAC,wBAAe;;;;;;;;;;sCAIlB,2BAAC,SAAG;4BACF,IAAI;4BACJ,IAAI;4BACJ,IAAI;4BACJ,IAAI;4BACJ,IAAI;4BACJ,KAAK;4BACL,OAAO;gCAAE,cAAc;oCAAE,IAAI;oCAAG,IAAI;gCAAE;4BAAE;sCAExC,cAAA,2BAAC,uBAAc;;;;;;;;;;sCAIjB,2BAAC,SAAG;4BACF,IAAI;4BACJ,IAAI;4BACJ,IAAI;4BACJ,IAAI;4BACJ,IAAI;4BACJ,KAAK;sCAEL,cAAA,2BAAC,qBAAY;;;;;;;;;;;;;;;;;;;;;0BAMnB,2BAAC,oBAAe;;;;;;;;;;;AAGtB;GA9FM;;QAC8B,aAAQ;;;KADtC;IAgGN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzGf;;CAEC;;;;4BAUY;;;eAAA;;;;;gCARc;;;;;;;;;AAQpB,MAAM;IACX;;GAEC,GACD,aAAa,eAAwC;QACnD,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CAAiB;QACtD,OAAO,SAAS,IAAI;IACtB;IAEA;;GAEC,GACD,aAAa,WAAW,OAA0B,EAAyB;QACzE,MAAM,WAAW,MAAM,mBAAU,CAAC,IAAI,CAAe,UAAU;QAC/D,OAAO,SAAS,IAAI;IACtB;IAEA;;GAEC,GACD,aAAa,WAAW,EAAU,EAAE,OAA0B,EAAyB;QACrF,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CAAe,CAAC,OAAO,EAAE,GAAG,CAAC,EAAE;QACpE,OAAO,SAAS,IAAI;IACtB;IAEA;;GAEC,GACD,aAAa,WAAW,EAAU,EAAiB;QACjD,MAAM,mBAAU,CAAC,MAAM,CAAC,CAAC,OAAO,EAAE,GAAG,CAAC;IACxC;IAEA;;GAEC,GACD,aAAa,eAA2C;QACtD,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CAAoB;QACzD,OAAO,SAAS,IAAI;IACtB;AACF"}