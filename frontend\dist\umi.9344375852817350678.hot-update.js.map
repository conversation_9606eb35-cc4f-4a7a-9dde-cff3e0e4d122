{"version": 3, "sources": ["umi.9344375852817350678.hot-update.js", "src/app.tsx", "src/utils/tokenUtils.ts"], "sourcesContent": ["globalThis.makoModuleHotUpdate(\r\n  'src/.umi/umi.ts?hmr',\r\n  {\r\n    modules: {},\r\n  },\r\n  function (runtime) {\r\n    runtime._h='2755697761087488972';\nruntime.updateEnsure2Map({\"src/.umi/core/EmptyRoute.tsx\":[\"src/.umi/core/EmptyRoute.tsx\"],\"src/.umi/plugin-layout/Layout.tsx\":[\"vendors\",\"src/.umi/plugin-layout/Layout.tsx\"],\"src/.umi/plugin-openapi/openapi.tsx\":[\"vendors\",\"src/.umi/plugin-openapi/openapi.tsx\"],\"src/pages/404.tsx\":[\"p__404\"],\"src/pages/Dashboard/index.tsx\":[\"p__Dashboard__index\"],\"src/pages/friend/index.tsx\":[\"p__friend__index\"],\"src/pages/help/index.tsx\":[\"p__help__index\"],\"src/pages/personal-center/index.tsx\":[\"src/pages/personal-center/index.tsx\"],\"src/pages/subscription/index.tsx\":[\"p__subscription__index\"],\"src/pages/team/detail/index.tsx\":[\"p__team__detail__index\"],\"src/pages/team/index.tsx\":[\"p__team__index\"],\"src/pages/user/index.tsx\":[\"p__user__index\"],\"src/pages/user/login/index.tsx\":[\"p__user__login__index\"]});;\r\n  },\r\n);\r\n", "\r\nimport type { Settings as LayoutSettings } from '@ant-design/pro-components';\r\nimport { SettingDrawer } from '@ant-design/pro-components';\r\nimport type { RequestConfig, RunTimeLayoutConfig } from '@umijs/max';\r\nimport { history } from '@umijs/max';\r\nimport {\r\n  Footer,\r\n  Question,\r\n  ErrorBoundary,\r\n  GlobalLoading,\r\n} from '@/components';\r\nimport UserFloatButton from '@/components/FloatButton';\r\nimport { AuthService, UserService, TeamService } from '@/services';\r\nimport type { UserProfileResponse, TeamDetailResponse } from '@/types/api';\r\nimport { parseJwtPayload } from '@/utils/tokenUtils';\r\nimport defaultSettings from '../config/defaultSettings';\r\nimport { errorConfig } from './requestErrorConfig';\r\nimport '@ant-design/v5-patch-for-react-19';\r\n\r\nconst isDev = process.env.NODE_ENV === 'development';\r\nconst loginPath = '/user/login';\r\nconst teamSelectPath = '/user/team-select';\r\nconst teamCreatePath = '/team/create';\r\n\r\n// 不需要认证的路径\r\nconst noAuthPaths = [\r\n  loginPath,\r\n  teamSelectPath,\r\n  '/404',\r\n];\r\n\r\n/**\r\n * 检查Token中是否包含团队信息\r\n */\r\nconst hasTeamInToken = (): boolean => {\r\n  const token = localStorage.getItem('auth_token');\r\n  if (!token) return false;\r\n\r\n  const payload = parseJwtPayload(token);\r\n  return payload && payload.teamId != null;\r\n};\r\n\r\n/**\r\n * 全局初始状态\r\n */\r\nexport interface InitialState {\r\n  settings?: Partial<LayoutSettings>;\r\n  currentUser?: UserProfileResponse;\r\n  currentTeam?: TeamDetailResponse;\r\n  loading?: boolean;\r\n  fetchUserInfo?: () => Promise<UserProfileResponse | undefined>;\r\n  fetchTeamInfo?: () => Promise<TeamDetailResponse | undefined>;\r\n}\r\n\r\n/**\r\n * @see https://umijs.org/docs/api/runtime-config#getinitialstate\r\n * */\r\nexport async function getInitialState(): Promise<InitialState> {\r\n  const fetchUserInfo = async (): Promise<UserProfileResponse | undefined> => {\r\n    try {\r\n      if (!AuthService.isLoggedIn()) {\r\n        return undefined;\r\n      }\r\n\r\n      const userProfile = await UserService.getUserProfile();\r\n      return userProfile;\r\n    } catch (error: any) {\r\n      console.error('获取用户信息失败:', error);\r\n\r\n      // 只有在确实是认证错误时才清除 Token\r\n      if (error?.message?.includes('401') || error?.message?.includes('未认证')) {\r\n        AuthService.clearToken();\r\n      }\r\n      return undefined;\r\n    }\r\n  };\r\n\r\n  const fetchTeamInfo = async (): Promise<TeamDetailResponse | undefined> => {\r\n    try {\r\n      const teamDetail = await TeamService.getCurrentTeamDetail();\r\n      return teamDetail;\r\n    } catch (error) {\r\n      console.error('获取团队信息失败:', error);\r\n      return undefined;\r\n    }\r\n  };\r\n\r\n  const { location } = history;\r\n  const currentPath = location.pathname;\r\n\r\n  // 如果是不需要认证的页面，直接返回基础状态\r\n  if (noAuthPaths.some(path => currentPath.startsWith(path))) {\r\n    return {\r\n      fetchUserInfo,\r\n      fetchTeamInfo,\r\n      settings: defaultSettings as Partial<LayoutSettings>,\r\n    };\r\n  }\r\n\r\n  // 检查认证状态 - 直接检查 localStorage 避免时序问题\r\n  const token = localStorage.getItem('auth_token');\r\n  const hasToken = !!token;\r\n\r\n  // 使用 localStorage 直接检查，避免 AuthService 的时序问题\r\n  if (!hasToken) {\r\n    history.push(loginPath);\r\n    return {\r\n      fetchUserInfo,\r\n      fetchTeamInfo,\r\n      settings: defaultSettings as Partial<LayoutSettings>,\r\n    };\r\n  }\r\n\r\n  // 获取用户信息\r\n  const currentUser = await fetchUserInfo();\r\n  if (!currentUser) {\r\n    history.push(loginPath);\r\n    return {\r\n      fetchUserInfo,\r\n      fetchTeamInfo,\r\n      settings: defaultSettings as Partial<LayoutSettings>,\r\n    };\r\n  }\r\n\r\n  // 获取团队信息\r\n  const currentTeam = await fetchTeamInfo();\r\n\r\n  // 如果没有团队信息且不在团队相关页面或个人中心页面，跳转到团队选择页面\r\n  if (!currentTeam && ![teamSelectPath, teamCreatePath, '/personal-center'].includes(currentPath)) {\r\n    history.push(teamSelectPath);\r\n    return {\r\n      fetchUserInfo,\r\n      fetchTeamInfo,\r\n      currentUser,\r\n      settings: defaultSettings as Partial<LayoutSettings>,\r\n    };\r\n  }\r\n\r\n  return {\r\n    fetchUserInfo,\r\n    fetchTeamInfo,\r\n    currentUser,\r\n    currentTeam,\r\n    settings: defaultSettings as Partial<LayoutSettings>,\r\n  };\r\n}\r\n\r\n// ProLayout 支持的api https://procomponents.ant.design/components/layout\r\nexport const layout: RunTimeLayoutConfig = ({\r\n  initialState,\r\n  setInitialState,\r\n}) => {\r\n  return {\r\n    actionsRender: () => [\r\n      <Question key=\"doc\" />,\r\n    ],\r\n    // 移除头像下拉菜单，使用浮动按钮替代\r\n    avatarProps: false,\r\n    waterMarkProps: {\r\n      content: initialState?.currentUser?.name,\r\n    },\r\n    footerRender: () => <Footer />,\r\n    onPageChange: () => {\r\n      const { location } = history;\r\n      const currentPath = location.pathname;\r\n\r\n      // 如果是不需要认证的页面，不做处理\r\n      if (noAuthPaths.some(path => currentPath.startsWith(path))) {\r\n        return;\r\n      }\r\n\r\n      // 检查登录状态\r\n      if (!AuthService.isLoggedIn()) {\r\n        history.push(loginPath);\r\n        return;\r\n      }\r\n\r\n      // 检查团队选择状态 - 使用 initialState.currentTeam\r\n      if (!initialState?.currentTeam && ![teamSelectPath, teamCreatePath].includes(currentPath)) {\r\n        history.push(teamSelectPath);\r\n        return;\r\n      }\r\n    },\r\n    bgLayoutImgList: [],\r\n    links: [],\r\n    menuHeaderRender: undefined,\r\n    childrenRender: (children) => {\r\n      return (\r\n        <ErrorBoundary>\r\n          <GlobalLoading>\r\n            {children}\r\n            {/* 添加浮动按钮 */}\r\n            <UserFloatButton />\r\n            {isDev && (\r\n              <SettingDrawer\r\n                disableUrlParams\r\n                enableDarkTheme\r\n                settings={initialState?.settings}\r\n                onSettingChange={(settings) => {\r\n                  setInitialState((preInitialState) => ({\r\n                    ...preInitialState,\r\n                    settings,\r\n                  }));\r\n                }}\r\n              />\r\n            )}\r\n          </GlobalLoading>\r\n        </ErrorBoundary>\r\n      );\r\n    },\r\n    title: initialState?.currentTeam?.name || 'TeamAuth',\r\n    logo: '/logo.svg',\r\n    ...initialState?.settings,\r\n  };\r\n};\r\n\r\n/**\r\n * @name request 配置，可以配置错误处理\r\n * 它基于 axios 和 ahooks 的 useRequest 提供了一套统一的网络请求和错误处理方案。\r\n * @doc https://umijs.org/docs/max/request#配置\r\n */\r\nexport const request: RequestConfig = {\r\n  baseURL: '/api', // 使用代理，相对路径\r\n  ...errorConfig,\r\n};\r\n", "/**\n * Token 解析工具\n * 用于直接从 JWT Token 中解析信息，避免依赖异步状态更新\n */\n\n/**\n * 解析 JWT Token 的 payload\n * @param token JWT Token\n * @returns 解析后的 payload 对象\n */\nfunction parseJwtPayload(token: string): any {\n  try {\n    // JWT Token 格式：header.payload.signature\n    const parts = token.split('.');\n    if (parts.length !== 3) {\n      throw new Error('Invalid JWT format');\n    }\n\n    // 解码 payload (base64url)\n    const payload = parts[1];\n    // 处理 base64url 编码（替换 - 和 _ 字符，添加必要的 padding）\n    const base64 = payload.replace(/-/g, '+').replace(/_/g, '/');\n    const paddedBase64 = base64 + '='.repeat((4 - base64.length % 4) % 4);\n    \n    // 解码并解析 JSON\n    const decodedPayload = atob(paddedBase64);\n    return JSON.parse(decodedPayload);\n  } catch (error) {\n    console.error('Failed to parse JWT payload:', error);\n    return null;\n  }\n}\n\n/**\n * 从当前 Token 中获取团队 ID\n * @returns 团队 ID，如果没有团队信息则返回 null\n */\nexport function getTeamIdFromCurrentToken(): number | null {\n  try {\n    const token = localStorage.getItem('auth_token');\n    if (!token) {\n      return null;\n    }\n\n    const payload = parseJwtPayload(token);\n    if (!payload) {\n      return null;\n    }\n\n    return payload.teamId || null;\n  } catch (error) {\n    console.error('Failed to get team ID from token:', error);\n    return null;\n  }\n}\n\n/**\n * 从当前 Token 中获取用户 ID\n * @returns 用户 ID，如果没有用户信息则返回 null\n */\nexport function getUserIdFromCurrentToken(): number | null {\n  try {\n    const token = localStorage.getItem('auth_token');\n    if (!token) {\n      return null;\n    }\n\n    const payload = parseJwtPayload(token);\n    if (!payload) {\n      return null;\n    }\n\n    return payload.userId || null;\n  } catch (error) {\n    console.error('Failed to get user ID from token:', error);\n    return null;\n  }\n}\n\n/**\n * 检查当前 Token 是否包含团队信息\n * @returns 是否包含团队信息\n */\nexport function hasTeamInCurrentToken(): boolean {\n  const teamId = getTeamIdFromCurrentToken();\n  return teamId !== null && teamId !== undefined;\n}\n\n/**\n * 从当前 Token 中获取是否为创建者\n * @returns 是否为创建者，如果没有团队信息则返回 null\n */\nexport function getIsCreatorFromCurrentToken(): boolean | null {\n  try {\n    const token = localStorage.getItem('auth_token');\n    if (!token) {\n      return null;\n    }\n\n    const payload = parseJwtPayload(token);\n    if (!payload) {\n      return null;\n    }\n\n    return payload.isCreator || null;\n  } catch (error) {\n    console.error('Failed to get isCreator from token:', error);\n    return null;\n  }\n}\n\n/**\n * 获取当前 Token 的完整信息\n * @returns Token 中的所有信息\n */\nexport function getCurrentTokenInfo(): {\n  userId: number | null;\n  teamId: number | null;\n  isCreator: boolean | null;\n  email: string | null;\n  name: string | null;\n} {\n  try {\n    const token = localStorage.getItem('auth_token');\n    if (!token) {\n      return {\n        userId: null,\n        teamId: null,\n        isCreator: null,\n        email: null,\n        name: null,\n      };\n    }\n\n    const payload = parseJwtPayload(token);\n    if (!payload) {\n      return {\n        userId: null,\n        teamId: null,\n        isCreator: null,\n        email: null,\n        name: null,\n      };\n    }\n\n    return {\n      userId: payload.userId || null,\n      teamId: payload.teamId || null,\n      isCreator: payload.isCreator || null,\n      email: payload.email || null,\n      name: payload.name || null,\n    };\n  } catch (error) {\n    console.error('Failed to get token info:', error);\n    return {\n      userId: null,\n      teamId: null,\n      isCreator: null,\n      email: null,\n      name: null,\n    };\n  }\n}\n"], "names": [], "mappings": "AAAA,WAAW,mBAAmB,CAC5B,uBACA;IACE,SAAS;;;;;;;;;;;;;gBCsDS,eAAe;2BAAf;;gBA2FT,MAAM;2BAAN;;gBAyEA,OAAO;2BAAP;;;;;;;kDA3NiB;wCAEN;+CAMjB;yFACqB;6CAC0B;+CAEtB;6FACJ;uDACA;6BACrB;;;;;;;;;YAEP,MAAM,QAAQ;YACd,MAAM,YAAY;YAClB,MAAM,iBAAiB;YACvB,MAAM,iBAAiB;YAEvB,WAAW;YACX,MAAM,cAAc;gBAClB;gBACA;gBACA;aACD;YAED;;CAEC,GACD,MAAM,iBAAiB;gBACrB,MAAM,QAAQ,aAAa,OAAO,CAAC;gBACnC,IAAI,CAAC,OAAO,OAAO;gBAEnB,MAAM,UAAU,IAAA,2BAAe,EAAC;gBAChC,OAAO,WAAW,QAAQ,MAAM,IAAI;YACtC;YAiBO,eAAe;gBACpB,MAAM,gBAAgB;oBACpB,IAAI;wBACF,IAAI,CAAC,qBAAW,CAAC,UAAU,IACzB,OAAO;wBAGT,MAAM,cAAc,MAAM,qBAAW,CAAC,cAAc;wBACpD,OAAO;oBACT,EAAE,OAAO,OAAY;4BAIf,gBAAmC;wBAHvC,QAAQ,KAAK,CAAC,aAAa;wBAE3B,uBAAuB;wBACvB,IAAI,CAAA,kBAAA,6BAAA,iBAAA,MAAO,OAAO,cAAd,qCAAA,eAAgB,QAAQ,CAAC,YAAU,kBAAA,6BAAA,kBAAA,MAAO,OAAO,cAAd,sCAAA,gBAAgB,QAAQ,CAAC,SAC9D,qBAAW,CAAC,UAAU;wBAExB,OAAO;oBACT;gBACF;gBAEA,MAAM,gBAAgB;oBACpB,IAAI;wBACF,MAAM,aAAa,MAAM,qBAAW,CAAC,oBAAoB;wBACzD,OAAO;oBACT,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,aAAa;wBAC3B,OAAO;oBACT;gBACF;gBAEA,MAAM,EAAE,QAAQ,EAAE,GAAG,YAAO;gBAC5B,MAAM,cAAc,SAAS,QAAQ;gBAErC,uBAAuB;gBACvB,IAAI,YAAY,IAAI,CAAC,CAAA,OAAQ,YAAY,UAAU,CAAC,QAClD,OAAO;oBACL;oBACA;oBACA,UAAU,wBAAe;gBAC3B;gBAGF,oCAAoC;gBACpC,MAAM,QAAQ,aAAa,OAAO,CAAC;gBACnC,MAAM,WAAW,CAAC,CAAC;gBAEnB,4CAA4C;gBAC5C,IAAI,CAAC,UAAU;oBACb,YAAO,CAAC,IAAI,CAAC;oBACb,OAAO;wBACL;wBACA;wBACA,UAAU,wBAAe;oBAC3B;gBACF;gBAEA,SAAS;gBACT,MAAM,cAAc,MAAM;gBAC1B,IAAI,CAAC,aAAa;oBAChB,YAAO,CAAC,IAAI,CAAC;oBACb,OAAO;wBACL;wBACA;wBACA,UAAU,wBAAe;oBAC3B;gBACF;gBAEA,SAAS;gBACT,MAAM,cAAc,MAAM;gBAE1B,qCAAqC;gBACrC,IAAI,CAAC,eAAe,CAAC;oBAAC;oBAAgB;oBAAgB;iBAAmB,CAAC,QAAQ,CAAC,cAAc;oBAC/F,YAAO,CAAC,IAAI,CAAC;oBACb,OAAO;wBACL;wBACA;wBACA;wBACA,UAAU,wBAAe;oBAC3B;gBACF;gBAEA,OAAO;oBACL;oBACA;oBACA;oBACA;oBACA,UAAU,wBAAe;gBAC3B;YACF;YAGO,MAAM,SAA8B,CAAC,EAC1C,YAAY,EACZ,eAAe,EAChB;oBAQc,2BAmDJ;gBA1DT,OAAO;oBACL,eAAe,IAAM;0CACnB,2BAAC,oBAAQ,MAAK;;;;;yBACf;oBACD,oBAAoB;oBACpB,aAAa;oBACb,gBAAgB;wBACd,OAAO,EAAE,yBAAA,oCAAA,4BAAA,aAAc,WAAW,cAAzB,gDAAA,0BAA2B,IAAI;oBAC1C;oBACA,cAAc,kBAAM,2BAAC,kBAAM;;;;;oBAC3B,cAAc;wBACZ,MAAM,EAAE,QAAQ,EAAE,GAAG,YAAO;wBAC5B,MAAM,cAAc,SAAS,QAAQ;wBAErC,mBAAmB;wBACnB,IAAI,YAAY,IAAI,CAAC,CAAA,OAAQ,YAAY,UAAU,CAAC,QAClD;wBAGF,SAAS;wBACT,IAAI,CAAC,qBAAW,CAAC,UAAU,IAAI;4BAC7B,YAAO,CAAC,IAAI,CAAC;4BACb;wBACF;wBAEA,yCAAyC;wBACzC,IAAI,EAAC,yBAAA,mCAAA,aAAc,WAAW,KAAI,CAAC;4BAAC;4BAAgB;yBAAe,CAAC,QAAQ,CAAC,cAAc;4BACzF,YAAO,CAAC,IAAI,CAAC;4BACb;wBACF;oBACF;oBACA,iBAAiB,EAAE;oBACnB,OAAO,EAAE;oBACT,kBAAkB;oBAClB,gBAAgB,CAAC;wBACf,qBACE,2BAAC,yBAAa;sCACZ,cAAA,2BAAC,yBAAa;;oCACX;kDAED,2BAAC,oBAAe;;;;;oCACf,uBACC,2BAAC,4BAAa;wCACZ,gBAAgB;wCAChB,eAAe;wCACf,QAAQ,EAAE,yBAAA,mCAAA,aAAc,QAAQ;wCAChC,iBAAiB,CAAC;4CAChB,gBAAgB,CAAC,kBAAqB,CAAA;oDACpC,GAAG,eAAe;oDAClB;gDACF,CAAA;wCACF;;;;;;;;;;;;;;;;;oBAMZ;oBACA,OAAO,CAAA,yBAAA,oCAAA,4BAAA,aAAc,WAAW,cAAzB,gDAAA,0BAA2B,IAAI,KAAI;oBAC1C,MAAM;uBACH,yBAAA,mCAAA,aAAc,QAAQ,AAAzB;gBACF;YACF;YAOO,MAAM,UAAyB;gBACpC,SAAS;gBACT,GAAG,+BAAW;YAChB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBC7GgB,mBAAmB;2BAAnB;;gBAvBA,4BAA4B;2BAA5B;;gBAvDA,yBAAyB;2BAAzB;;gBAuBA,yBAAyB;2BAAzB;;gBAuBA,qBAAqB;2BAArB;;;;;;;;;;;;;YAnFhB;;;CAGC,GAED;;;;CAIC,GACD,SAAS,gBAAgB,KAAa;gBACpC,IAAI;oBACF,wCAAwC;oBACxC,MAAM,QAAQ,MAAM,KAAK,CAAC;oBAC1B,IAAI,MAAM,MAAM,KAAK,GACnB,MAAM,IAAI,MAAM;oBAGlB,yBAAyB;oBACzB,MAAM,UAAU,KAAK,CAAC,EAAE;oBACxB,6CAA6C;oBAC7C,MAAM,SAAS,QAAQ,OAAO,CAAC,MAAM,KAAK,OAAO,CAAC,MAAM;oBACxD,MAAM,eAAe,SAAS,IAAI,MAAM,CAAC,AAAC,CAAA,IAAI,OAAO,MAAM,GAAG,CAAA,IAAK;oBAEnE,aAAa;oBACb,MAAM,iBAAiB,KAAK;oBAC5B,OAAO,KAAK,KAAK,CAAC;gBACpB,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,gCAAgC;oBAC9C,OAAO;gBACT;YACF;YAMO,SAAS;gBACd,IAAI;oBACF,MAAM,QAAQ,aAAa,OAAO,CAAC;oBACnC,IAAI,CAAC,OACH,OAAO;oBAGT,MAAM,UAAU,gBAAgB;oBAChC,IAAI,CAAC,SACH,OAAO;oBAGT,OAAO,QAAQ,MAAM,IAAI;gBAC3B,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,qCAAqC;oBACnD,OAAO;gBACT;YACF;YAMO,SAAS;gBACd,IAAI;oBACF,MAAM,QAAQ,aAAa,OAAO,CAAC;oBACnC,IAAI,CAAC,OACH,OAAO;oBAGT,MAAM,UAAU,gBAAgB;oBAChC,IAAI,CAAC,SACH,OAAO;oBAGT,OAAO,QAAQ,MAAM,IAAI;gBAC3B,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,qCAAqC;oBACnD,OAAO;gBACT;YACF;YAMO,SAAS;gBACd,MAAM,SAAS;gBACf,OAAO,WAAW,QAAQ,WAAW;YACvC;YAMO,SAAS;gBACd,IAAI;oBACF,MAAM,QAAQ,aAAa,OAAO,CAAC;oBACnC,IAAI,CAAC,OACH,OAAO;oBAGT,MAAM,UAAU,gBAAgB;oBAChC,IAAI,CAAC,SACH,OAAO;oBAGT,OAAO,QAAQ,SAAS,IAAI;gBAC9B,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,uCAAuC;oBACrD,OAAO;gBACT;YACF;YAMO,SAAS;gBAOd,IAAI;oBACF,MAAM,QAAQ,aAAa,OAAO,CAAC;oBACnC,IAAI,CAAC,OACH,OAAO;wBACL,QAAQ;wBACR,QAAQ;wBACR,WAAW;wBACX,OAAO;wBACP,MAAM;oBACR;oBAGF,MAAM,UAAU,gBAAgB;oBAChC,IAAI,CAAC,SACH,OAAO;wBACL,QAAQ;wBACR,QAAQ;wBACR,WAAW;wBACX,OAAO;wBACP,MAAM;oBACR;oBAGF,OAAO;wBACL,QAAQ,QAAQ,MAAM,IAAI;wBAC1B,QAAQ,QAAQ,MAAM,IAAI;wBAC1B,WAAW,QAAQ,SAAS,IAAI;wBAChC,OAAO,QAAQ,KAAK,IAAI;wBACxB,MAAM,QAAQ,IAAI,IAAI;oBACxB;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,6BAA6B;oBAC3C,OAAO;wBACL,QAAQ;wBACR,QAAQ;wBACR,WAAW;wBACX,OAAO;wBACP,MAAM;oBACR;gBACF;YACF;;;;;;;;;;;;;;;;;;;;;;;IF/Jc;AACZ,GACA,SAAU,OAAO;IACf,QAAQ,EAAE,GAAC;IACf,QAAQ,gBAAgB,CAAC;QAAC,gCAA+B;YAAC;SAA+B;QAAC,qCAAoC;YAAC;YAAU;SAAoC;QAAC,uCAAsC;YAAC;YAAU;SAAsC;QAAC,qBAAoB;YAAC;SAAS;QAAC,iCAAgC;YAAC;SAAsB;QAAC,8BAA6B;YAAC;SAAmB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,uCAAsC;YAAC;SAAsC;QAAC,oCAAmC;YAAC;SAAyB;QAAC,mCAAkC;YAAC;SAAyB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,kCAAiC;YAAC;SAAwB;IAAA;;AAC1xB"}