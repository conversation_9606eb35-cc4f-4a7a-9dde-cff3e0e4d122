globalThis.makoModuleHotUpdate('src/.umi/umi.ts?hmr', {
    modules: {
        "src/utils/tokenUtils.ts": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            function _export(target, all) {
                for(var name in all)Object.defineProperty(target, name, {
                    enumerable: true,
                    get: all[name]
                });
            }
            __mako_require__.e(exports, {
                getCurrentTokenInfo: function() {
                    return getCurrentTokenInfo;
                },
                getIsCreatorFromCurrentToken: function() {
                    return getIsCreatorFromCurrentToken;
                },
                getTeamIdFromCurrentToken: function() {
                    return getTeamIdFromCurrentToken;
                },
                getUserIdFromCurrentToken: function() {
                    return getUserIdFromCurrentToken;
                },
                hasTeamInCurrentToken: function() {
                    return hasTeamInCurrentToken;
                },
                parseJwtPayload: function() {
                    return parseJwtPayload;
                }
            });
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            function parseJwtPayload(token) {
                try {
                    // JWT Token 格式：header.payload.signature
                    const parts = token.split('.');
                    if (parts.length !== 3) throw new Error('Invalid JWT format');
                    // 解码 payload (base64url)
                    const payload = parts[1];
                    // 处理 base64url 编码（替换 - 和 _ 字符，添加必要的 padding）
                    const base64 = payload.replace(/-/g, '+').replace(/_/g, '/');
                    const paddedBase64 = base64 + '='.repeat((4 - base64.length % 4) % 4);
                    // 解码并解析 JSON
                    const decodedPayload = atob(paddedBase64);
                    return JSON.parse(decodedPayload);
                } catch (error) {
                    console.error('Failed to parse JWT payload:', error);
                    return null;
                }
            }
            function getTeamIdFromCurrentToken() {
                try {
                    const token = localStorage.getItem('auth_token');
                    if (!token) return null;
                    const payload = parseJwtPayload(token);
                    if (!payload) return null;
                    return payload.teamId || null;
                } catch (error) {
                    console.error('Failed to get team ID from token:', error);
                    return null;
                }
            }
            function getUserIdFromCurrentToken() {
                try {
                    const token = localStorage.getItem('auth_token');
                    if (!token) return null;
                    const payload = parseJwtPayload(token);
                    if (!payload) return null;
                    return payload.userId || null;
                } catch (error) {
                    console.error('Failed to get user ID from token:', error);
                    return null;
                }
            }
            function hasTeamInCurrentToken() {
                const teamId = getTeamIdFromCurrentToken();
                return teamId !== null && teamId !== undefined;
            }
            function getIsCreatorFromCurrentToken() {
                try {
                    const token = localStorage.getItem('auth_token');
                    if (!token) return null;
                    const payload = parseJwtPayload(token);
                    if (!payload) return null;
                    return payload.isCreator || null;
                } catch (error) {
                    console.error('Failed to get isCreator from token:', error);
                    return null;
                }
            }
            function getCurrentTokenInfo() {
                try {
                    const token = localStorage.getItem('auth_token');
                    if (!token) return {
                        userId: null,
                        teamId: null,
                        isCreator: null,
                        email: null,
                        name: null
                    };
                    const payload = parseJwtPayload(token);
                    if (!payload) return {
                        userId: null,
                        teamId: null,
                        isCreator: null,
                        email: null,
                        name: null
                    };
                    return {
                        userId: payload.userId || null,
                        teamId: payload.teamId || null,
                        isCreator: payload.isCreator || null,
                        email: payload.email || null,
                        name: payload.name || null
                    };
                } catch (error) {
                    console.error('Failed to get token info:', error);
                    return {
                        userId: null,
                        teamId: null,
                        isCreator: null,
                        email: null,
                        name: null
                    };
                }
            }
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        }
    }
}, function(runtime) {
    runtime._h = '18020996624373155257';
    runtime.updateEnsure2Map({
        "src/.umi/core/EmptyRoute.tsx": [
            "src/.umi/core/EmptyRoute.tsx"
        ],
        "src/.umi/plugin-layout/Layout.tsx": [
            "vendors",
            "src/.umi/plugin-layout/Layout.tsx"
        ],
        "src/.umi/plugin-openapi/openapi.tsx": [
            "vendors",
            "src/.umi/plugin-openapi/openapi.tsx"
        ],
        "src/pages/404.tsx": [
            "p__404"
        ],
        "src/pages/Dashboard/index.tsx": [
            "p__Dashboard__index"
        ],
        "src/pages/friend/index.tsx": [
            "p__friend__index"
        ],
        "src/pages/help/index.tsx": [
            "p__help__index"
        ],
        "src/pages/personal-center/index.tsx": [
            "src/pages/personal-center/index.tsx"
        ],
        "src/pages/subscription/index.tsx": [
            "p__subscription__index"
        ],
        "src/pages/team/detail/index.tsx": [
            "p__team__detail__index"
        ],
        "src/pages/team/index.tsx": [
            "p__team__index"
        ],
        "src/pages/user/index.tsx": [
            "p__user__index"
        ],
        "src/pages/user/login/index.tsx": [
            "p__user__login__index"
        ]
    });
    ;
});

//# sourceMappingURL=umi.2173864377623496243.hot-update.js.map