import React, { useState, useEffect } from "react";
import {
  Card,
  Typography,
  Tooltip,
  List,
  Flex,
  Spin,
  Alert,
  message,
  Row,
  Col
} from "antd";
import { TeamService } from "@/services/team";
import { AuthService } from "@/services";
import { useModel, history } from '@umijs/max';
import type { TeamDetailResponse } from "@/types/api";
import {
  CarOutlined,
  TeamOutlined,
  UserOutlined,
  ClockCircleOutlined,
  CrownOutlined,
  RightOutlined,
  ExclamationCircleOutlined
} from "@ant-design/icons";

const { Text, Title } = Typography;

// 响应式布局样式
const styles = `
  .team-item .ant-card-body {
    padding: 0 !important;
  }

  .team-item:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1) !important;
  }

  @media (max-width: 768px) {
    .team-item {
      margin-bottom: 8px;
    }

    .team-stats-row {
      margin-top: 8px;
    }
  }

  @media (max-width: 576px) {
    .team-stats-row {
      margin-top: 12px;
    }

    .team-stats-col {
      margin-bottom: 4px;
    }

    .team-info-wrap {
      gap: 8px !important;
    }
  }

  @media (max-width: 480px) {
    .team-name-text {
      font-size: 14px !important;
    }

    .team-meta-text {
      font-size: 11px !important;
    }
  }
`;

const TeamListCard: React.FC = () => {
  // 团队列表状态管理
  const [teams, setTeams] = useState<TeamDetailResponse[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [switchingTeamId, setSwitchingTeamId] = useState<number | null>(null);

  const { initialState, setInitialState } = useModel('@@initialState');
  const currentTeam = initialState?.currentTeam;

  // 获取团队列表数据
  useEffect(() => {
    const fetchTeams = async () => {
      try {
        setLoading(true);
        setError(null);
        const teamsData = await TeamService.getUserTeamsWithStats();
        setTeams(teamsData);
      } catch (error) {
        console.error('获取团队列表失败:', error);
        setError('获取团队列表失败');
      } finally {
        setLoading(false);
      }
    };

    fetchTeams();
  }, []);

  // 团队切换处理函数
  const handleTeamSwitch = async (teamId: number, teamName: string) => {
    try {
      setSwitchingTeamId(teamId);

      // 如果是当前团队，直接跳转到仪表盘，不需要调用切换API
      if (teamId === currentTeam?.id) {
        message.success(`进入团队：${teamName}`);
        history.push('/dashboard');
        return;
      }

      // 非当前团队，执行切换逻辑
      const response = await AuthService.selectTeam({ teamId });

      // 检查后端返回的团队选择成功标识
      if (response.teamSelectionSuccess && response.team && response.team.id === teamId) {
        message.success(`已切换到团队：${teamName}`);

        // 同步更新 initialState，等待更新完成后再跳转
        if (initialState?.fetchTeamInfo && initialState?.fetchUserInfo && setInitialState) {
          try {
            const [currentUser, currentTeam] = await Promise.all([
              initialState.fetchUserInfo(),
              initialState.fetchTeamInfo()
            ]);

            // 确保团队信息已正确获取
            if (currentTeam && currentTeam.id === teamId) {
              await setInitialState({
                ...initialState,
                currentUser,
                currentTeam,
              });

              // 等待 initialState 更新完成后再跳转到仪表盘
              setTimeout(() => {
                history.push('/dashboard');
              }, 100);
            } else {
              console.error('获取的团队信息与选择的团队不匹配');
              message.error('团队切换失败，请重试');
            }
          } catch (error) {
            console.error('更新 initialState 失败:', error);
            message.error('团队切换失败，请重试');
          }
        } else {
          // 如果没有 initialState 相关方法，直接跳转
          history.push('/dashboard');
        }
      } else {
        console.error('团队切换响应异常，未返回正确的团队信息');
        message.error('团队切换失败，请重试');
      }
    } catch (error) {
      console.error('团队切换失败:', error);
      message.error('团队切换失败');
    } finally {
      setSwitchingTeamId(null);
    }
  };

  return (
    <>
      {/* 注入样式 */}
      <style dangerouslySetInnerHTML={{ __html: styles }} />

      <Card
        className="dashboard-card"
        style={{
          borderRadius: 16,
          boxShadow: "0 6px 20px rgba(0,0,0,0.08)",
          border: "none",
          background: "linear-gradient(145deg, #ffffff, #f8faff)",
        }}
        title={
          <Flex justify="space-between" align="center">
            <Title level={4} style={{
              margin: 0,
              background: 'linear-gradient(135deg, #1890ff, #722ed1)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              fontWeight: 600
            }}>
              团队列表
            </Title>
          </Flex>
        }
      >
      {error ? (
        <Alert
          message="团队列表加载失败"
          description={error}
          type="error"
          showIcon
          style={{ marginBottom: 16 }}
        />
      ) : (
        <Spin spinning={loading}>
          <List
            dataSource={teams}
            renderItem={(item) => (
              <List.Item>
                <Card
                  className="team-item"
                  style={{
                    background: currentTeam?.id === item.id
                      ? "linear-gradient(135deg, #f0f9ff, #e6f4ff)"
                      : "#fff",
                    borderRadius: 8,
                    boxShadow: currentTeam?.id === item.id
                      ? "0 2px 8px rgba(24, 144, 255, 0.12)"
                      : "0 1px 4px rgba(0,0,0,0.06)",
                    width: "100%",
                    borderLeft: `3px solid ${item.isCreator ? "#722ed1" : "#52c41a"}`,
                    transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
                    border: currentTeam?.id === item.id
                      ? "1px solid #91caff"
                      : "1px solid #f0f0f0",
                    padding: "12px 16px",
                    position: 'relative',
                    overflow: 'hidden'
                  }}
                  hoverable
                  onMouseEnter={(e) => {
                    if (currentTeam?.id !== item.id) {
                      e.currentTarget.style.transform = 'translateY(-2px)';
                      e.currentTarget.style.boxShadow = '0 8px 24px rgba(0,0,0,0.12)';
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (currentTeam?.id !== item.id) {
                      e.currentTarget.style.transform = 'translateY(0)';
                      e.currentTarget.style.boxShadow = '0 2px 8px rgba(0,0,0,0.06)';
                    }
                  }}
                >

                  {/* 响应式布局 */}
                  <Row gutter={[8, 8]} align="middle" style={{ width: '100%' }}>
                    {/* 左侧：团队信息 */}
                    <Col xs={24} sm={24} md={14} lg={12} xl={14}>
                      <Flex vertical gap={6}>
                        {/* 团队名称行 */}
                        <Flex align="center" gap={8} wrap="wrap">
                          <div
                            style={{
                              cursor: 'pointer',
                              padding: '2px 4px',
                              borderRadius: 4,
                              transition: 'all 0.2s ease',
                              display: 'flex',
                              alignItems: 'center',
                              gap: 6
                            }}
                            onClick={() => handleTeamSwitch(item.id, item.name)}
                            onMouseEnter={(e) => {
                              e.currentTarget.style.background = 'rgba(24, 144, 255, 0.05)';
                            }}
                            onMouseLeave={(e) => {
                              e.currentTarget.style.background = 'transparent';
                            }}
                          >
                            <Text
                              strong
                              style={{
                                fontSize: 16,
                                color: currentTeam?.id === item.id ? '#1890ff' : '#262626',
                                lineHeight: 1.2
                              }}
                            >
                              {item.name}
                            </Text>
                            <RightOutlined
                              style={{
                                fontSize: 10,
                                color: currentTeam?.id === item.id ? '#1890ff' : '#8c8c8c',
                                verticalAlign: 'middle',
                                display: 'inline-flex',
                                alignItems: 'center'
                              }}
                            />
                          </div>

                          {/* 状态标识 */}
                          {currentTeam?.id === item.id && (
                            <span style={{
                              background: '#1890ff',
                              color: 'white',
                              padding: '1px 6px',
                              borderRadius: 8,
                              fontSize: 10,
                              fontWeight: 500
                            }}>
                              当前
                            </span>
                          )}

                          {switchingTeamId === item.id && (
                            <Flex align="center" gap={4}>
                              <Spin size="small" />
                              <Text style={{ fontSize: 10, color: '#666' }}>切换中</Text>
                            </Flex>
                          )}
                        </Flex>

                        {/* 团队基本信息 */}
                        <Flex align="center" gap={12} wrap="wrap">
                          <Tooltip title={`创建时间: ${new Date(item.createdAt).toLocaleString('zh-CN')}`}>
                            <Flex align="center" gap={4}>
                              <ClockCircleOutlined style={{ color: "#8c8c8c", fontSize: 12 }} />
                              <Text style={{ fontSize: 12, color: '#8c8c8c' }}>
                                {new Date(item.createdAt).toLocaleDateString('zh-CN')}
                              </Text>
                            </Flex>
                          </Tooltip>

                          <Tooltip title={`团队成员: ${item.memberCount}人`}>
                            <Flex align="center" gap={4}>
                              <TeamOutlined style={{ color: "#8c8c8c", fontSize: 12 }} />
                              <Text style={{ fontSize: 12, color: '#8c8c8c' }}>
                                {item.memberCount} 人
                              </Text>
                            </Flex>
                          </Tooltip>

                          {/* 角色标识 */}
                          <span style={{
                            background: item.isCreator ? '#722ed1' : '#52c41a',
                            color: 'white',
                            padding: '2px 6px',
                            borderRadius: 8,
                            fontSize: 10,
                            fontWeight: 500,
                            display: 'flex',
                            alignItems: 'center',
                            gap: 2
                          }}>
                            {item.isCreator ? (
                              <>
                                <CrownOutlined style={{ fontSize: 9 }} />
                                管理员
                              </>
                            ) : (
                              <>
                                <UserOutlined style={{ fontSize: 9 }} />
                                成员
                              </>
                            )}
                          </span>
                        </Flex>
                      </Flex>
                    </Col>

                    {/* 右侧：响应式指标卡片 */}
                    <Col xs={24} sm={24} md={10} lg={12} xl={10}>
                      <Row gutter={[4, 4]} justify={{ xs: "start", md: "end" }}>
                        {/* 车辆资源 */}
                        <Col xs={6} sm={6} md={6} lg={6} xl={6}>
                          <div style={{
                            background: '#f0f7ff',
                            border: '1px solid #d9e8ff',
                            borderRadius: 6,
                            padding: '4px 6px',
                            textAlign: 'center',
                            minWidth: '45px'
                          }}>
                            <Flex vertical align="center" gap={1}>
                              <CarOutlined style={{ color: "#1890ff", fontSize: 12 }} />
                              <Text strong style={{ fontSize: 14, color: '#1890ff', lineHeight: 1 }}>
                                {item.stats?.vehicles || 0}
                              </Text>
                              <Text style={{ fontSize: 8, color: '#666' }}>车辆</Text>
                            </Flex>
                          </div>
                        </Col>

                        {/* 人员资源 */}
                        <Col xs={6} sm={6} md={6} lg={6} xl={6}>
                          <div style={{
                            background: '#f6ffed',
                            border: '1px solid #d1f0be',
                            borderRadius: 6,
                            padding: '4px 6px',
                            textAlign: 'center',
                            minWidth: '45px'
                          }}>
                            <Flex vertical align="center" gap={1}>
                              <UserOutlined style={{ color: "#52c41a", fontSize: 12 }} />
                              <Text strong style={{ fontSize: 14, color: '#52c41a', lineHeight: 1 }}>
                                {item.stats?.personnel || 0}
                              </Text>
                              <Text style={{ fontSize: 8, color: '#666' }}>人员</Text>
                            </Flex>
                          </div>
                        </Col>

                        {/* 临期事项 */}
                        <Col xs={6} sm={6} md={6} lg={6} xl={6}>
                          <div style={{
                            background: '#fff7e6',
                            border: '1px solid #ffd666',
                            borderRadius: 6,
                            padding: '4px 6px',
                            textAlign: 'center',
                            minWidth: '45px'
                          }}>
                            <Flex vertical align="center" gap={1}>
                              <ExclamationCircleOutlined style={{ color: "#faad14", fontSize: 12 }} />
                              <Text strong style={{ fontSize: 14, color: '#faad14', lineHeight: 1 }}>
                                {item.stats?.expiring || 0}
                              </Text>
                              <Text style={{ fontSize: 8, color: '#666' }}>临期</Text>
                            </Flex>
                          </div>
                        </Col>

                        {/* 逾期事项 */}
                        <Col xs={6} sm={6} md={6} lg={6} xl={6}>
                          <div style={{
                            background: '#fff1f0',
                            border: '1px solid #ffccc7',
                            borderRadius: 6,
                            padding: '4px 6px',
                            textAlign: 'center',
                            minWidth: '45px'
                          }}>
                            <Flex vertical align="center" gap={1}>
                              <ExclamationCircleOutlined style={{ color: "#ff4d4f", fontSize: 12 }} />
                              <Text strong style={{ fontSize: 14, color: '#ff4d4f', lineHeight: 1 }}>
                                {item.stats?.overdue || 0}
                              </Text>
                              <Text style={{ fontSize: 8, color: '#666' }}>逾期</Text>
                            </Flex>
                          </div>
                        </Col>
                      </Row>
                    </Col>
                  </Row>
                </Card>
              </List.Item>
            )}
          />
        </Spin>
      )}
    </Card>
    </>
  );
};

export default TeamListCard;