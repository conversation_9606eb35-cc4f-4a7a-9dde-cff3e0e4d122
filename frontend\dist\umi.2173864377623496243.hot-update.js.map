{"version": 3, "sources": ["umi.2173864377623496243.hot-update.js", "src/utils/tokenUtils.ts"], "sourcesContent": ["globalThis.makoModuleHotUpdate(\r\n  'src/.umi/umi.ts?hmr',\r\n  {\r\n    modules: {},\r\n  },\r\n  function (runtime) {\r\n    runtime._h='18020996624373155257';\nruntime.updateEnsure2Map({\"src/.umi/core/EmptyRoute.tsx\":[\"src/.umi/core/EmptyRoute.tsx\"],\"src/.umi/plugin-layout/Layout.tsx\":[\"vendors\",\"src/.umi/plugin-layout/Layout.tsx\"],\"src/.umi/plugin-openapi/openapi.tsx\":[\"vendors\",\"src/.umi/plugin-openapi/openapi.tsx\"],\"src/pages/404.tsx\":[\"p__404\"],\"src/pages/Dashboard/index.tsx\":[\"p__Dashboard__index\"],\"src/pages/friend/index.tsx\":[\"p__friend__index\"],\"src/pages/help/index.tsx\":[\"p__help__index\"],\"src/pages/personal-center/index.tsx\":[\"src/pages/personal-center/index.tsx\"],\"src/pages/subscription/index.tsx\":[\"p__subscription__index\"],\"src/pages/team/detail/index.tsx\":[\"p__team__detail__index\"],\"src/pages/team/index.tsx\":[\"p__team__index\"],\"src/pages/user/index.tsx\":[\"p__user__index\"],\"src/pages/user/login/index.tsx\":[\"p__user__login__index\"]});;\r\n  },\r\n);\r\n", "/**\n * Token 解析工具\n * 用于直接从 JWT Token 中解析信息，避免依赖异步状态更新\n */\n\n/**\n * 解析 JWT Token 的 payload\n * @param token JWT Token\n * @returns 解析后的 payload 对象\n */\nexport function parseJwtPayload(token: string): any {\n  try {\n    // JWT Token 格式：header.payload.signature\n    const parts = token.split('.');\n    if (parts.length !== 3) {\n      throw new Error('Invalid JWT format');\n    }\n\n    // 解码 payload (base64url)\n    const payload = parts[1];\n    // 处理 base64url 编码（替换 - 和 _ 字符，添加必要的 padding）\n    const base64 = payload.replace(/-/g, '+').replace(/_/g, '/');\n    const paddedBase64 = base64 + '='.repeat((4 - base64.length % 4) % 4);\n    \n    // 解码并解析 JSON\n    const decodedPayload = atob(paddedBase64);\n    return JSON.parse(decodedPayload);\n  } catch (error) {\n    console.error('Failed to parse JWT payload:', error);\n    return null;\n  }\n}\n\n/**\n * 从当前 Token 中获取团队 ID\n * @returns 团队 ID，如果没有团队信息则返回 null\n */\nexport function getTeamIdFromCurrentToken(): number | null {\n  try {\n    const token = localStorage.getItem('auth_token');\n    if (!token) {\n      return null;\n    }\n\n    const payload = parseJwtPayload(token);\n    if (!payload) {\n      return null;\n    }\n\n    return payload.teamId || null;\n  } catch (error) {\n    console.error('Failed to get team ID from token:', error);\n    return null;\n  }\n}\n\n/**\n * 从当前 Token 中获取用户 ID\n * @returns 用户 ID，如果没有用户信息则返回 null\n */\nexport function getUserIdFromCurrentToken(): number | null {\n  try {\n    const token = localStorage.getItem('auth_token');\n    if (!token) {\n      return null;\n    }\n\n    const payload = parseJwtPayload(token);\n    if (!payload) {\n      return null;\n    }\n\n    return payload.userId || null;\n  } catch (error) {\n    console.error('Failed to get user ID from token:', error);\n    return null;\n  }\n}\n\n/**\n * 检查当前 Token 是否包含团队信息\n * @returns 是否包含团队信息\n */\nexport function hasTeamInCurrentToken(): boolean {\n  const teamId = getTeamIdFromCurrentToken();\n  return teamId !== null && teamId !== undefined;\n}\n\n/**\n * 从当前 Token 中获取是否为创建者\n * @returns 是否为创建者，如果没有团队信息则返回 null\n */\nexport function getIsCreatorFromCurrentToken(): boolean | null {\n  try {\n    const token = localStorage.getItem('auth_token');\n    if (!token) {\n      return null;\n    }\n\n    const payload = parseJwtPayload(token);\n    if (!payload) {\n      return null;\n    }\n\n    return payload.isCreator || null;\n  } catch (error) {\n    console.error('Failed to get isCreator from token:', error);\n    return null;\n  }\n}\n\n/**\n * 获取当前 Token 的完整信息\n * @returns Token 中的所有信息\n */\nexport function getCurrentTokenInfo(): {\n  userId: number | null;\n  teamId: number | null;\n  isCreator: boolean | null;\n  email: string | null;\n  name: string | null;\n} {\n  try {\n    const token = localStorage.getItem('auth_token');\n    if (!token) {\n      return {\n        userId: null,\n        teamId: null,\n        isCreator: null,\n        email: null,\n        name: null,\n      };\n    }\n\n    const payload = parseJwtPayload(token);\n    if (!payload) {\n      return {\n        userId: null,\n        teamId: null,\n        isCreator: null,\n        email: null,\n        name: null,\n      };\n    }\n\n    return {\n      userId: payload.userId || null,\n      teamId: payload.teamId || null,\n      isCreator: payload.isCreator || null,\n      email: payload.email || null,\n      name: payload.name || null,\n    };\n  } catch (error) {\n    console.error('Failed to get token info:', error);\n    return {\n      userId: null,\n      teamId: null,\n      isCreator: null,\n      email: null,\n      name: null,\n    };\n  }\n}\n"], "names": [], "mappings": "AAAA,WAAW,mBAAmB,CAC5B,uBACA;IACE,SAAS;;;;;;;;;;;;;gBCgHG,mBAAmB;2BAAnB;;gBAvBA,4BAA4B;2BAA5B;;gBAvDA,yBAAyB;2BAAzB;;gBAuBA,yBAAyB;2BAAzB;;gBAuBA,qBAAqB;2BAArB;;gBAzEA,eAAe;2BAAf;;;;;;;;;;;;;YAAT,SAAS,gBAAgB,KAAa;gBAC3C,IAAI;oBACF,wCAAwC;oBACxC,MAAM,QAAQ,MAAM,KAAK,CAAC;oBAC1B,IAAI,MAAM,MAAM,KAAK,GACnB,MAAM,IAAI,MAAM;oBAGlB,yBAAyB;oBACzB,MAAM,UAAU,KAAK,CAAC,EAAE;oBACxB,6CAA6C;oBAC7C,MAAM,SAAS,QAAQ,OAAO,CAAC,MAAM,KAAK,OAAO,CAAC,MAAM;oBACxD,MAAM,eAAe,SAAS,IAAI,MAAM,CAAC,AAAC,CAAA,IAAI,OAAO,MAAM,GAAG,CAAA,IAAK;oBAEnE,aAAa;oBACb,MAAM,iBAAiB,KAAK;oBAC5B,OAAO,KAAK,KAAK,CAAC;gBACpB,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,gCAAgC;oBAC9C,OAAO;gBACT;YACF;YAMO,SAAS;gBACd,IAAI;oBACF,MAAM,QAAQ,aAAa,OAAO,CAAC;oBACnC,IAAI,CAAC,OACH,OAAO;oBAGT,MAAM,UAAU,gBAAgB;oBAChC,IAAI,CAAC,SACH,OAAO;oBAGT,OAAO,QAAQ,MAAM,IAAI;gBAC3B,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,qCAAqC;oBACnD,OAAO;gBACT;YACF;YAMO,SAAS;gBACd,IAAI;oBACF,MAAM,QAAQ,aAAa,OAAO,CAAC;oBACnC,IAAI,CAAC,OACH,OAAO;oBAGT,MAAM,UAAU,gBAAgB;oBAChC,IAAI,CAAC,SACH,OAAO;oBAGT,OAAO,QAAQ,MAAM,IAAI;gBAC3B,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,qCAAqC;oBACnD,OAAO;gBACT;YACF;YAMO,SAAS;gBACd,MAAM,SAAS;gBACf,OAAO,WAAW,QAAQ,WAAW;YACvC;YAMO,SAAS;gBACd,IAAI;oBACF,MAAM,QAAQ,aAAa,OAAO,CAAC;oBACnC,IAAI,CAAC,OACH,OAAO;oBAGT,MAAM,UAAU,gBAAgB;oBAChC,IAAI,CAAC,SACH,OAAO;oBAGT,OAAO,QAAQ,SAAS,IAAI;gBAC9B,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,uCAAuC;oBACrD,OAAO;gBACT;YACF;YAMO,SAAS;gBAOd,IAAI;oBACF,MAAM,QAAQ,aAAa,OAAO,CAAC;oBACnC,IAAI,CAAC,OACH,OAAO;wBACL,QAAQ;wBACR,QAAQ;wBACR,WAAW;wBACX,OAAO;wBACP,MAAM;oBACR;oBAGF,MAAM,UAAU,gBAAgB;oBAChC,IAAI,CAAC,SACH,OAAO;wBACL,QAAQ;wBACR,QAAQ;wBACR,WAAW;wBACX,OAAO;wBACP,MAAM;oBACR;oBAGF,OAAO;wBACL,QAAQ,QAAQ,MAAM,IAAI;wBAC1B,QAAQ,QAAQ,MAAM,IAAI;wBAC1B,WAAW,QAAQ,SAAS,IAAI;wBAChC,OAAO,QAAQ,KAAK,IAAI;wBACxB,MAAM,QAAQ,IAAI,IAAI;oBACxB;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,6BAA6B;oBAC3C,OAAO;wBACL,QAAQ;wBACR,QAAQ;wBACR,WAAW;wBACX,OAAO;wBACP,MAAM;oBACR;gBACF;YACF;;;;;;;;;;;;;;;;;;;;;;;ID/Jc;AACZ,GACA,SAAU,OAAO;IACf,QAAQ,EAAE,GAAC;IACf,QAAQ,gBAAgB,CAAC;QAAC,gCAA+B;YAAC;SAA+B;QAAC,qCAAoC;YAAC;YAAU;SAAoC;QAAC,uCAAsC;YAAC;YAAU;SAAsC;QAAC,qBAAoB;YAAC;SAAS;QAAC,iCAAgC;YAAC;SAAsB;QAAC,8BAA6B;YAAC;SAAmB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,uCAAsC;YAAC;SAAsC;QAAC,oCAAmC;YAAC;SAAyB;QAAC,mCAAkC;YAAC;SAAyB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,kCAAiC;YAAC;SAAwB;IAAA;;AAC1xB"}