/**
 * 团队选择页面
 * 实现双阶段认证的第二阶段：团队登录
 */

import React, { useState, useEffect } from 'react';
import { Card, Typography, Space, message, Spin } from 'antd';
import { TeamOutlined } from '@ant-design/icons';
import { history, useLocation, useModel } from '@umijs/max';
import { createStyles } from 'antd-style';
import { AuthService, TeamService } from '@/services';
import type { TeamInfo } from '@/types/api';
import { TeamList, ActionButtons } from './components';

const { Title, Text } = Typography;

const useStyles = createStyles(({ token }) => {
  return {
    container: {
      display: 'flex',
      flexDirection: 'column',
      height: '100vh',
      overflow: 'auto',
      backgroundColor: token.colorBgLayout,
    },
    content: {
      flex: 1,
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'center',
      alignItems: 'center',
      padding: '32px 16px',
    },
    header: {
      marginBottom: 40,
      textAlign: 'center',
    },
    teamCard: {
      width: '100%',
      maxWidth: 600,
      marginBottom: 24,
    },
  };
});

const TeamSelectPage: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [selectedTeamId, setSelectedTeamId] = useState<number | null>(null);
  const [teams, setTeams] = useState<TeamInfo[]>([]);
  const { styles } = useStyles();
  const location = useLocation();
  const { initialState, setInitialState } = useModel('@@initialState');

  useEffect(() => {
    // 从路由状态获取团队列表
    const teamsFromState = location.state?.teams;
    if (teamsFromState) {
      setTeams(teamsFromState);
      // 不自动选中团队，需要用户手动选择
    } else {
      // 如果没有团队数据，重新获取
      fetchTeams();
    }
  }, [location.state]);

  const fetchTeams = async () => {
    try {
      setLoading(true);
      const teamList = await TeamService.getUserTeams();
      const teamInfos: TeamInfo[] = teamList.map(team => ({
        id: team.id,
        name: team.name,
        isCreator: team.isCreator,
        memberCount: team.memberCount,
        lastAccessTime: team.updatedAt,
      }));
      setTeams(teamInfos);
    } catch (error) {
      console.error('获取团队列表失败:', error);
      message.error('获取团队列表失败');
    } finally {
      setLoading(false);
    }
  };

  const handleTeamSelect = async () => {
    if (!selectedTeamId) {
      message.warning('请选择一个团队');
      return;
    }

    setLoading(true);

    try {
      const response = await AuthService.selectTeam({ teamId: selectedTeamId });

      // 检查后端返回的团队选择成功标识
      if (response.teamSelectionSuccess && response.team && response.team.id === selectedTeamId) {
        message.success('团队选择成功！');

        // 由于Token已经更新，路由守卫现在能够正确识别团队信息，可以直接跳转
        // 同时异步更新 initialState 以保持状态同步
        if (initialState?.fetchTeamInfo && initialState?.fetchUserInfo && setInitialState) {
          // 异步更新状态，不阻塞跳转
          Promise.all([
            initialState.fetchUserInfo(),
            initialState.fetchTeamInfo()
          ]).then(([currentUser, currentTeam]) => {
            if (currentTeam && currentTeam.id === selectedTeamId) {
              setInitialState({
                ...initialState,
                currentUser,
                currentTeam,
              });
            }
          }).catch(error => {
            console.error('更新 initialState 失败:', error);
          });
        }

        // 直接跳转，路由守卫会处理团队验证
        history.push('/dashboard');
      } else {
        console.error('团队选择响应异常，未返回正确的团队信息');
        message.error('团队选择失败，请重试');
      }
    } catch (error) {
      console.error('团队选择失败:', error);
      message.error('团队选择失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  // 保持兼容性的方法名
  const handleTeamLogin = handleTeamSelect;

  const handleCreateTeam = () => {
    history.push('/team/create');
  };

  const handleLogout = () => {
    AuthService.clearTokens();
    history.push('/user/login');
  };

  if (loading && teams.length === 0) {
    return (
      <div className={styles.container}>
        <div className={styles.content}>
          <Spin size="large" />
        </div>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      <div className={styles.content}>
        <div className={styles.header}>
          <Space direction="vertical" align="center" size="large">
            <TeamOutlined style={{ fontSize: 48, color: '#1890ff' }} />
            <div>
              <Title level={2}>选择团队</Title>
              <Text type="secondary">请选择要进入的团队工作空间</Text>
            </div>
          </Space>
        </div>

        <Card className={styles.teamCard}>
          <TeamList
            teams={teams}
            selectedTeamId={selectedTeamId}
            onTeamSelect={setSelectedTeamId}
          />
        </Card>

        <ActionButtons
          hasTeams={teams.length > 0}
          selectedTeamId={selectedTeamId}
          loading={loading}
          onTeamLogin={handleTeamLogin}
          onCreateTeam={handleCreateTeam}
          onLogout={handleLogout}
          showCreateButton={true} // 始终显示创建团队按钮
        />
      </div>
    </div>
  );
};

export default TeamSelectPage;
